<?php 
defined('BASEPATH') or exit('No direct script access allowed');

class Payroll extends CI_Controller {
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        //This should come up only for super admins
        if (!$this->authorization->isModuleEnabled('PAYROLL') || !$this->authorization->isAuthorized('PAYROLL.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('payroll_model');
        $this->load->library('filemanager');
        $this->load->model('Config_model', 'cmodel');
        $this->load->model('staff/Staff_Payroll_Model');
    }

    public function index() {
        $data['permit_add_salary_structure'] =  $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN');
        $data['permit_administration'] = $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN');
        $data['permit_payslip_generation'] = $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN');
        $data['permit_payslip_reports'] = $this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS');
        $data['permit_my_payslips'] = $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS');
        $data['permit_disbursement'] = $this->authorization->isAuthorized('PAYROLL.DISBURSEMENT');
        $data['permit_pf_reports'] = $this->authorization->isAuthorized('PAYROLL.PF_REPORTS');
        $data['permit_pt_reports'] = $this->authorization->isAuthorized('PAYROLL.PT_REPORTS');
        $data['permit_staff_wise_reports'] = $this->authorization->isAuthorized('PAYROLL.STAFF_WISE_REPORT');
        // echo "<pre>";print_r($data);die();

        $site_url = site_url();
        $data['tiles'] = array(
            [
                'title' => 'Salary Structure',
                'sub_title' => 'Manage Salary structure of your staff',
                'icon' => 'svg_icons/salarystructure.svg',
                'url' => $site_url.'management/payroll/showPayrollData',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            // [
            // 'title' => 'Entitled Salary',
            // 'sub_title' => 'Entitled Salary',
            // 'icon' => 'svg_icons/payslipgeneration.svg',
            // 'url' => $site_url.'management/payroll/entitled_earned_salary/',
            // 'permission' => $this->authorization->isAuthorized('PAYROLL.ENTITLED_SALARY')
            // ],
            //     Since no longer used it is being removed.
            //     Recommended by mohan sir, 09-12-2024.
            // [
            // 'title' => 'Payslip Generation',
            // 'sub_title' => 'Generate payslips',
            // 'icon' => 'svg_icons/payslipgeneration.svg',
            // 'url' => $site_url.'management/payroll/schedulePayroll/',
            // 'permission' => $this->authorization->isAuthorized('PAYROLL.INDIVIDUAL_PAYROLL_GENERATION')
            // ],
            [
                'title' => 'Mass Payslip Generation',
                'sub_title' => 'Generate Mass Payslips',
                'icon' => 'svg_icons/payslipgeneration.svg',
                'url' => $site_url.'management/payroll/mass_schedulePayroll/',
                'permission' => $this->authorization->isAuthorized('PAYROLL.MASS_PAYSLIP_GENERATION')
            ],
            [
                'title' => 'Payslip Approval',
                'sub_title' => 'Payslip Approval for Staff',
                'icon' => 'svg_icons/mastersettings.svg',
                'url' => $site_url.'management/payroll/payslip_approval',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            [
                'title' => 'Disbursement',
                'sub_title' => '',
                'icon' => 'svg_icons/feereceiptsupdate.svg',
                'url' => $site_url.'management/payroll/disbursement',
                'permission' => $this->authorization->isAuthorized('PAYROLL.DISBURSEMENT')
            ],
            [
                'title' => 'Manage Increments',
                'sub_title' => 'Payroll Increments for Staff',
                'icon' => 'svg_icons/feetype.svg',
                'url' => $site_url.'management/payroll/manage_increments',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            [
                'title' => 'Manage Increments Approvals',
                'sub_title' => 'Payroll Increments for Staff',
                'icon' => 'svg_icons/staffpfreport.svg',
                'url' => $site_url.'management/payroll/manage_increments_approvals',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            [
                'title' => 'Salary Advance',
                'sub_title' => 'Import / Export Salary Structure',
                'icon' => 'svg_icons/staffpfreport.svg',
                'url' => $site_url.'management/payroll/salary_advance',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            // [
            //     'title' => 'My Payslips',
            //     'sub_title' => 'View your personal payslips',
            //     'icon' =>  'svg_icons/feereceiptsupdate.svg',
            //     'url' => $site_url.'staff/Payroll_controller/index',
            //     'permission' => $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS')
            // ]
        );
         // echo "<pre>";print_r($data);die();
        $data['tiles'] = checkTilePermissions($data['tiles']);

        $data['report_tiles'] = array(
            [
                'title' => 'Payslip summary report',
                'sub_title' => '',
                'icon' => 'svg_icons/payslipsummaryreport.svg',
                'url' => $site_url.'management/payroll/reports/',
                'permission' => $this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')
            ],
            [
                'title' => 'Annual Summary Report',
                'sub_title' => '',
                'icon' => 'svg_icons/annualsummaryreport.svg',
                'url' => $site_url.'management/payroll/annual_summary_report',
                'permission' => $this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')
            ],
            [
                'title' => 'Staff Summary Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffsummaryreport.svg',
                'url' => $site_url.'management/payroll/staff_summary_report',
                'permission' => $this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')
            ],
            [
                'title' => 'Staff PF Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffpfreport.svg',
                'url' => $site_url.'management/payroll/pf_reports',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PF_REPORTS')
            ],
            [
                'title' => 'Staff PT Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffptreport.svg',
                'url' => $site_url.'management/payroll/pt_reports',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PT_REPORTS')
            ],
            [
                'title' => 'Staff ESI Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffptreport.svg',
                'url' => $site_url.'management/payroll/esi_reports',
                'permission' => $this->authorization->isAuthorized('PAYROLL.ESI_REPORTS')
            ],
            [
                'title' => 'Other Deductions Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffptreport.svg',
                'url' => $site_url.'management/payroll/other_deduction_report',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PT_REPORTS')
            ],
            [
                'title' => 'Staff Wise Payroll Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffsummaryreport.svg',
                'url' => $site_url.'management/payroll/staff_wise_payroll_report',
                'permission' => $this->authorization->isAuthorized('PAYROLL.STAFF_WISE_REPORT')
            ],
            [
                'title' => 'Increments Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffsummaryreport.svg',
                'url' => $site_url.'management/payroll/incrementsReport',
                'permission' => $this->authorization->isAuthorized('PAYROLL.INCREMENTS_REPORT')
            ],
            [
                'title' => 'Payroll Edit History Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffsummaryreport.svg',
                'url' => $site_url.'management/payroll/payroll_edit_history_report',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_HISTORY_REPORT')
            ],
        );
        $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
    
        $data['standard_income_tax_reports'] = array(
            [
                'title' => 'Month-wise TDS Deductee Report',
                'sub_title' => '',
                'icon' => 'svg_icons/staffsummaryreport.svg',
                'url' => $site_url.'management/payroll/monthwiseTdsDeducteeReport',
                'permission' => $this->authorization->isAuthorized('PAYROLL.MONTHWISE_TDS_DEDUCTEE_REPORT')
            ],
            // [
            //     'title' => 'Standard Income Tax Report',
            //     'sub_title' => '',
            //     'icon' => 'svg_icons/staffsummaryreport.svg',
            //     'url' => $site_url.'management/payroll/standardIncomeTaxReport',
            //     'permission' => $this->authorization->isAuthorized('PAYROLL.STANDARD_INCOME_TAX_REPORT')
            // ],
        );
        $data['standard_income_tax_reports'] = checkTilePermissions($data['standard_income_tax_reports']);

        $data['administration_tiles'] = array(
            [
                'title' => 'Master Settings',
                'sub_title' => 'Manage master settings of the school',
                'icon' => 'svg_icons/mastersettings.svg',
                // 'url' => $site_url.'management/payroll/settings',
                'url' => $site_url.'management/payroll/payroll_column_settings',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            [
                'title' => 'Manage Payroll Financial Years',
                'sub_title' => 'Manage payroll financial years',
                'icon' => 'svg_icons/finacialyears.svg',
                'url' => $site_url.'management/payroll/view_financial_years',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            [
                'title' => 'Manage Payroll Schedules',
                'sub_title' => 'Manage payroll schedule',
                'icon' => 'svg_icons/payrollschedule.svg',
                'url' => $site_url.'management/payroll/view_payroll_schedules',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            [
                'title' => 'Manage Payroll Slabs',
                'sub_title' => 'Manage payroll slabs',
                'icon' => 'svg_icons/payrollslabs.svg',
                'url' => $site_url.'management/payroll/slab',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            // [
            //     'title' => 'Staff Loan',
            //     'sub_title' => 'Manage Paroll Slabs for the school',
            //     'icon' => 'svg_icons/canteencollection.svg',
            //     'url' => $site_url.'management/payroll/loan_index',
            //     'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            // ],
            // [
            //     'title' => 'Import TDS or Reimbursement',
            //     'sub_title' => 'Payslip TDS/Reimbursement',
            //     'icon' => 'svg_icons/staffpfreport.svg',
            //     'url' => $site_url.'management/payroll/import_excelto_erp',
            //     'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            // ],
            [
                'title' => 'Mass Salary Structure Update',
                'sub_title' => 'Import / Export Salary Structure',
                'icon' => 'svg_icons/staffpfreport.svg',
                'url' => $site_url.'management/payroll/export_excel_salary_structure',
                'permission' => $this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')
            ],
            [
                'title' => 'Manage Investment Declaration',
                'sub_title' => 'Staff Incometax Declaration',
                'icon' => 'svg_icons/staffpfreport.svg',
                'url' => $site_url.'management/payroll/manage_income',
                'permission' => $this->authorization->isAuthorized('PAYROLL.MANAGE_TAX_DECLARATION')
            ],
            [
                'title' => 'Manage Increment Types',
                'sub_title' => 'Manage Increment Types',
                'icon' => 'svg_icons/staffptreport.svg',
                'url' => $site_url.'management/payroll/increment_types',
                'permission' => $this->authorization->isSuperAdmin()
            ],
            [
                'title' => 'Re-generate payslip',
                'sub_title' => 'Re-generate payslip',
                'icon' => 'svg_icons/staffptreport.svg',
                'url' => $site_url.'management/payroll/re_generate_payslip',
                'permission' => $this->authorization->isAuthorized('PAYROLL.RE_GENERATE_PAYSLIP')
            ]
        );
        $data['administration_tiles'] = checkTilePermissions($data['administration_tiles']);
        $data['type'] = 'Payroll';
        $data['back_url'] = site_url('management/payroll/');
        // echo "<pre>";print_r($data);die();
        $data['main_content'] = 'management/payroll/index';
        $this->load->view('inc/template', $data);
    }

    public function schedulePayroll($selected_month ='', $schedule_year = '') {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }

        $data['sStatus'] = $this->settings->getSetting('staff_status');
        $data['adSelected'] = '2';

        $stafType = $this->input->post('staff_type');
        if (!empty($stafType)) {
            $stafType =$stafType;
        }else{
            $stafType='all';
        }

        $schedules = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $present_month = (empty($schedules))?'':$schedules[0]['id'];

        foreach ($schedules as $key => &$val) {
            $val['scMonth'] = 'Not Selected';
            if (date('m',strtotime($val['start_date'])) == date('m', strtotime(date('Y-m')." -1 month"))) {
                $val['scMonth'] = 'Selected';
                $present_month = $val['id'];
            }
        }
        if (!empty($selected_month)) {
            $data['selected_schedule'] = $selected_month;
        }else{
            $data['selected_schedule'] = $present_month;
        }
        
        $data['schedules'] = $schedules;
        $data['schedule_year'] = $schedule_year;

        $data['templateData'] = $this->payroll_model->getTemplateData($present_month, $stafType, $data['adSelected']);
        // echo "<pre>"; print_r($data['sStatus']); die();
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['selected_staff_type'] = $stafType;
        
        $data['main_content'] = 'management/payroll/monthly_payroll';
        $this->load->view('inc/template', $data);
    }

    public function get_financial_yearwise_data() {
        $schedule_year = $_POST['schedule_year'];
        $result = $this->payroll_model->get_financial_yearwise_data($schedule_year);
        echo json_encode($result);
    }

    public function schedulePayroll_onchagne(){
        $selected_schedule = $_POST['selected_schedule'];
        $stafftypeId = $_POST['stafftypeId'];
        $staff_status = $_POST['staff_status'];
        $result = $this->payroll_model->getTemplateData($selected_schedule, $stafftypeId, $staff_status);
        echo json_encode($result);
    }

    public function showPayrollData() {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $this->config->load('form_elements');
        $data['employmentType'] = $this->config->item('employmentType');
        $data['sStatus'] = $this->settings->getSetting('staff_status');
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['adSelected'] = '2';
        $data['main_content'] = 'management/payroll/staff_payroll_data';
        $this->load->view('inc/template', $data);
    }

    public function stafftypeId_onchagne(){
        $stafftypeId = $_POST['stafftypeId'];
        $staff_status = $_POST['staff_status'];
        $employment_type = $_POST['employment_type'];
        $result = $this->payroll_model->get_all_staff_data_payroll($stafftypeId, $staff_status, $employment_type);     
        // foreach ($result as $key => $res) {
        //     if (empty($res->yearly_ctc)) {
        //         unset($result[$key]);
        //     }
        // }
        // Re-index the array
        // $result = array_values($result);
        // die();
        echo json_encode($result);
    }

    public function addPayrollDetails($staff_id){
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['staff_id'] = $staff_id;
        $data['staff'] = $this->payroll_model->getStaffname($staff_id);
        $data['slabs'] = $this->payroll_model->get_payroll_settings();
        $columns = $this->payroll_model->get_payroll_column_table();
        if(!empty($columns)){
            $earnings = [];
            $deduction = [];
            foreach ($columns->payroll_column as $key => $val) {
                if($val->type == 'earnings' && $val->salary_structure == '1'){
                    array_push($earnings, $val);
                }
                if($val->type == 'deduction' && $val->salary_structure == '1'){
                    array_push($deduction, $val);
                }
            }

            usort($earnings, function($a, $b) {
                // Convert empty strings to a very large value to push them to the end
                $orderA = ($a->order_by === "") ? PHP_INT_MAX : $a->order_by;
                $orderB = ($b->order_by === "") ? PHP_INT_MAX : $b->order_by;
            
                // Compare order_by values
                return $orderA - $orderB;
            });
            $data['earnings'] = $earnings;
            $data['deduction'] = $deduction;
            $data['main_content'] = 'management/payroll/add_staff_salary_dynamic';
        }else{
            $data['main_content'] = 'management/payroll/add_staff_salary';
        }
        //$data['main_content'] = 'management/payroll/schedulePayroll/'.$selected_month;
        $this->load->view('inc/template', $data);
    }

    public function editPayrollDetails($prId){
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        // $data['staff_id'] = $staff_id;
        // $data['staff'] = $this->payroll_model->getStaffname($staff_id);
        // $data['source'] = $source;
        // $data['selected_month'] = $selected_month;
        $data['current_details'] = $this->payroll_model->getStaffPayrollDetails($prId);
        // echo "<pre>"; print_r($data['current_details']); die();
        $data['slabs'] = $this->payroll_model->get_payroll_settings();
        $basic_mode = 2;
        $current_slab_id = $data['current_details']['slab_id'];
        foreach ($data['slabs'] as $slab) {
            if ($slab->id == $current_slab_id) {
                $basic_mode = $slab->basic_mode;
                break; // stop the loop once found
            }
        }
        $data['slab_basic_mode'] = $basic_mode;
        $columns = $this->payroll_model->get_payroll_column_table();
        if(!empty($columns)){
            $earnings = [];
            $deduction = [];
            foreach ($columns->payroll_column as $key => $val) {
                if($val->type == 'earnings' && $val->salary_structure == '1'){
                    array_push($earnings, $val);
                }
                if($val->type == 'deduction' && $val->salary_structure == '1'){
                    array_push($deduction, $val);
                }
            }

            usort($earnings, function($a, $b) {
                // Convert empty strings to a very large value to push them to the end
                $orderA = ($a->order_by === "") ? PHP_INT_MAX : $a->order_by;
                $orderB = ($b->order_by === "") ? PHP_INT_MAX : $b->order_by;
            
                // Compare order_by values
                return $orderA - $orderB;
            });
            $data['earnings'] = $earnings;
            $data['deduction'] = $deduction;
            $data['payroll_structure'] = $this->payroll_model->get_payroll_structure_by_id($prId);
            $data['main_content'] = 'management/payroll/edit_staff_salary_new';
        }else{
            $data['main_content'] = 'management/payroll/edit_staff_salary';
        }

        //if($source == 1)
        $this->load->view('inc/template', $data);
    }

    public function editPayrollDetails_payslip_page($staff_id) {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['current_details'] = $this->payroll_model->getStaffPayrollDetails_payslip_page($staff_id);
        $data['slabs'] = $this->payroll_model->get_payroll_settings();
        $data['main_content'] = 'management/payroll/edit_staff_salary';
        $this->load->view('inc/template', $data);
    }

    public function create_payslip($staff_id, $selected_schedule, $schedule_year) {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['staff_id'] = $staff_id;
        $data['staff_data'] = $this->payroll_model->getPayrollData($staff_id);

        $data['scheduled_data'] = $this->payroll_model->getScheduleData($selected_schedule);
        // $data['source'] = $source;
        $data['selected'] = $selected_schedule;
        $data['schedule_year'] = $schedule_year;
        $data['leave_info'] = $this->payroll_model->getLeaveInfo($staff_id, $selected_schedule);
        $data['leave_info_new'] = $this->payroll_model->getStaffLeaveInfo($staff_id, $selected_schedule);
        $data['show_biometric_data'] = $this->settings->getSetting('show_biometric_data_in_payroll');
        $data['tds_reimbursement'] = $this->payroll_model->get_tds_reimbursement($staff_id, $selected_schedule);
        $data['biometric_data'] = array();
        if($data['show_biometric_data']) {
            $data['biometric_data'] = $this->payroll_model->getBiometricData($staff_id, $data['scheduled_data']['start_date'], $data['scheduled_data']['end_date']);
        }
        $data['loan_repayment'] = $this->payroll_model->get_loan_repayment_selectedSchdule($staff_id, $selected_schedule);

        $data['main_content'] = 'management/payroll/create_staff_payroll';
        $this->load->view('inc/template', $data);
    }


    public function save_salary() {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        // echo '<pre>'; print_r($_POST); die();
        $selected_month = $_POST['selected_month'];
        $result = $this->payroll_model->save_salary();
        if ($_POST['source'] ==  'schedule') {
            if ($result){
            }
            else{
                $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect("management/payroll/schedulePayroll/" . $selected_month);
            }
        } else {
            if ($result){
            }
            else
                $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect("management/payroll/showPayrollData");
        }
    }


    public function update_salary($pmid) {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $result = $this->payroll_model->update_salary($pmid);
        if ($result){
            redirect("management/payroll/showPayrollData");
        }
        else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect("management/payroll/showPayrollData");
        }
    }


    public function submit_payrolls() {
        //echo '<pre>'; print_r($_POST); die();
        $result = $this->payroll_model->submit_payrolls();
    }

    public function save_payroll() {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        // $template_code = $this->payroll_model->get_templateCode(1);
        // // echo '<pre>'; print_r($template_code); 

        // // echo '<pre>'; print_r($_POST); die();
        // $keys = array_keys($_POST);
        // $_POST['dob'] = date('d-m-Y', strtotime($_POST['dob']));
        // $_POST['joining_date'] = date('d-m-Y', strtotime($_POST['joining_date']));
        // foreach ($keys as $key) {
        //     $template_code = str_replace('%%' . $key . '%%', $_POST[$key], $template_code);
        // }

        // $school_name = $this->payroll_model->getSchoolName();
        // // $noofleaves = $this->payroll_model->get_leaves($staff_id, $selected_month);
        // $noofleaves = $this->payroll_model->get_leaves($_POST['staff_id'], $_POST['schedule_id']);

        // $template_code = str_replace('%%school_name%%', $school_name, $template_code);
        // $template_code = str_replace('%%noofleaves%%', $noofleaves, $template_code);
        // $template_code = str_replace('%%from_date%%', $_POST['start_date'], $template_code);
        // $template_code = str_replace('%%to_date%%', $_POST['end_date'], $template_code);

        // // echo '<pre>'; print_r($template_code); 
        
        // $insert_id = $this->payslipPdf($template_code);

        $result = $this->payroll_model->save_payroll();
        
        sleep(1);
        if ($result) {
            redirect("management/payroll/view_payslip/" . $_POST['staff_id'] . "/" . $_POST['schedule_id']);
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect("management/payroll/create_payslip/" . $_POST['staff_id'] . "/" . $_POST['schedule_id']);
        }

        // if ($result)
        //     $this->session->set_flashdata('flashSuccess', 'Payroll Generated for ' . $_POST['staff_name']);
        // else
        //     $this->session->set_flashdata('flashError', 'Something went wrong.');
        // if ($_POST['flag'] == 1) {
        //     redirect("management/payroll/view_payslip/" . $_POST['staff_id'] . "/" . $_POST['schedule_id']);
        // } else {
        //     //$temp = $this->payroll->get_next_staff_id($_POST['staff_id']);
        //     $temp = $_POST['id'] + 1;
        //     redirect("management/payroll/create_payslip/" . $temp . "/schedule/" . $_POST['schedule_id'] . '');
        // }
    }

    public function view_payslip($staff_id, $selected_month) {
        $data['payslip'] = $this->payroll_model->get_payslip_info($staff_id, $selected_month);
        // echo "<pre>"; print_r($data['payslip']); die();
        $data['school_name'] = $this->payroll_model->getSchoolName();
        $data['noofleaves'] = $this->payroll_model->get_leaves($staff_id, $selected_month);
        $data['template_id'] = 1;
        $data['payroll_working_days'] = $this->settings->getSetting('payroll_working_days');
        $data['staff_id'] = $staff_id;
        $data['selecte_month'] = $selected_month;
        $template = $this->payroll_model->get_email_template_for_payslip();
        if($template){
            $pdf_html = $this->_create_template_payslip($data['payslip'],$template);
            $update =  $this->payroll_model->update_payroll_html_receipt($pdf_html, $staff_id, $selected_month);
            $this->genearte_payslip_pdf($pdf_html, $staff_id, $selected_month);
        }
        $school_name = $this->settings->getSetting('school_short_name');
        if ($school_name == 'iisb' || $school_name == 'iish') {
            $data['main_content'] = 'management/payroll/view_payslip_indus';       
        }elseif ($school_name == 'manchesterglobal') {  
            $data['main_content'] = 'management/payroll/view_payslip_manchester';
        }elseif ($school_name == 'iisp') {  
            $data['main_content'] = 'management/payroll/view_payslip_iisp';
        }else  {
            $data['main_content'] = 'management/payroll/view_payslip';
        }   
        $this->load->view('inc/template_fee', $data);
    }

    private function genearte_payslip_pdf($html, $staff_id, $selected_month){
        $school = CONFIG_ENV['main_folder'];
        $path = $school.'/payslips/'.uniqid().'-'.time().".pdf";
        $bucket = $this->config->item('s3_bucket');
        $status = $this->payroll_model->updatePayslipPath($staff_id, $selected_month, $path);
        
        $page = 'portrait';
        $page_size = 'a4';

        $curl = curl_init();
        $postData = urlencode($html);
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url().'Callback_Controller/updatePayrollPdfLink';

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        return $response;
        
    }

    public function edit_payslip($staff_id, $selected_month) {
        // $data['school_name'] = $this->payroll_model->getSchoolName();
        //print_r($data['school_name']); die();
        $data['payslip'] = $this->payroll_model->edit_payslip_staff_wise($staff_id, $selected_month);
        // echo "<pre>"; print_r($data['payslip']); die();
        $data['noofleaves'] = $this->payroll_model->get_leaves($staff_id, $selected_month);
        $data['leave_info'] = $this->payroll_model->getLeaveInfo($staff_id, $selected_month);
        $data['leave_info_new'] = $this->payroll_model->getStaffLeaveInfo($staff_id, $selected_month);
        $data['scheduled_data'] = $this->payroll_model->getScheduleData($selected_month);
        $data['show_biometric_data'] = $this->settings->getSetting('show_biometric_data_in_payroll');
        $data['biometric_data'] = array();
        if($data['show_biometric_data']) {
            $data['biometric_data'] = $this->payroll_model->getBiometricData($staff_id, $data['scheduled_data']['start_date'], $data['scheduled_data']['end_date']);    
        }
        $data['loan_repayment'] = $this->payroll_model->get_loan_repayment_selectedSchdule($staff_id, $selected_month);
        // echo '<pre>'; print_r($data['payslip']); die();
        $data['main_content'] = 'management/payroll/edit_payslip';
        $this->load->view('inc/template', $data);
    }

    public function new_payroll_schedule() {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['years'] = $this->payroll_model->get_fin_years();
        // echo '<pre>'; print_r($data); die();
        $data['title'] = 'Add Payroll Schedule';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/new_payroll_schedule';
        }
        $this->load->view('inc/template', $data);
    }

    public function new_financial_year() {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['main_content'] = 'management/payroll/new_f_years';
        $this->load->view('inc/template', $data);
    }

    public function save_payroll_schedule() {
        //echo '<pre>'; print_r($_POST); die();
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $result = $this->payroll_model->save_payroll_schedule();
        // if ($result)
        //     $this->session->set_flashdata('flashSuccess', 'Payroll Schedule is saved.');
        // else
        //     $this->session->set_flashdata('flashError', 'Something went wrong.');
        sleep(1);
        redirect("management/payroll/view_payroll_schedules");
    }

    public function reports($selected_month = '') {
        if (!$this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')) {
            redirect('dashboard', 'refresh');
        }
        $this->config->load('form_elements');
        $data['employmentType'] = $this->config->item('employmentType');
        $data['sType'] = $this->settings->getSetting('staff_type');
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $current_month = date('n');
        $current_date = date('Y-m-d');
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $start_date = date('Y-m-d', strtotime($financial_year->from_date));
            $start_year = date('Y', strtotime($start_date));
            $start_month = date('n', strtotime($start_date));

            if ($current_month >= 4) {
                if ($current_date >= $start_date && $current_year == $start_year) {
                    $selected_financial_year = $financial_year->id;
                    break;
                }
            } else {
                if ($current_date >= $start_date && ($current_year - 1) == $start_year) {
                    $selected_financial_year = $financial_year->id;
                    break;
                }
            }
        }
        if ($selected_financial_year === null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }

        $data['selected_financial_year'] = $selected_financial_year;

        $payroll_columns = $this->payroll_model->get_fields_columns_payroll();
        $enabled_fields = $this->cmodel->getEnabledValues('payroll');
        $eFields = json_decode($enabled_fields);
        $enabledOptions = array();
        foreach ($payroll_columns as $columns) {
            $found = 0;
            if (!empty($eFields)) {
                foreach ($eFields as $eColumn) {
                    if ($columns ==  $eColumn) {
                    $found = 1;
                    break;
                    }
                }
            }
            if ($found) {
                $enabledOptions [] =$columns;
            } else {
                $disabledOptions [] = $columns;
            }
            
        }
        $data['payroll_columns'] = json_encode($disabledOptions);
        $data['selected_columns'] = json_encode($enabledOptions);
        // echo '<pre>'; print_r($enabledOptions); die();
        $data['schedules'] = $this->payroll_model->get_schedules();
        
        $currentDate = date('Y-m-d');
        $selectedMonth = null;
        $present_month = '';

        foreach ($data['schedules'] as $schedule) {
            if ($schedule['end_date'] < $currentDate) {
                $selectedMonth = $schedule['schedule_name'];
                $present_month = $schedule['id'];
            } else {
                break;
            }
        }
        if (!empty($selected_month)) {
            $data['selected_schedule'] = $selected_month;
        }else{
            $data['selected_schedule'] = $present_month;
        }     
        $data['reports'] = $this->payroll_model->get_reports($data['selected_schedule']);
        // $data['schedules'] = $this->payroll_model->get_schedules();

        $columns = $this->payroll_model->get_payroll_column_table();
        
        if(!empty($columns)){
            $data['title'] = 'Payslip Summary Report';
            if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
                $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
            }else{
                $data['main_content'] = 'management/payroll/reports_new';
            }
        }else{
            $data['title'] = 'Payslip Summary Report';
            if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
                $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
            }else{
                $data['main_content'] = 'management/payroll/reports';
            }
        }
        // $data['main_content'] = 'management/payroll/reports';
        $this->load->view('inc/template', $data);
    }

    public function get_payroll_reports() {
        $selected_schedule = $this->input->post('selected_schedule');
        $reports = $this->payroll_model->get_reports($selected_schedule);
        
        if (!empty($reports)) {
            echo json_encode(['status' => 'success', 'data' => $reports]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'No data found']);
        }
    }

    public function reports_data(){
        $selected_schedule = $this->input->post('schedule_id');
        $staff_type = $this->input->post('staff_type');
        $employment_type = $this->input->post('employment_type');
        $data['reports'] = $this->payroll_model->get_reports($selected_schedule, $staff_type, $employment_type);
        $sType = $this->settings->getSetting('staff_type');
        if (empty($sType)) {
            foreach ($data['reports'] as &$report) {
                $report['staff_type'] = "<span style='color: red;'>Staff Type Not Found</span>";
            }
        } else {
            foreach ($data['reports'] as &$report) {
                $staffTypeIndex = $report['staff_type'];
                $report['staff_type'] = $sType[$staffTypeIndex] ?? "<span style='color: red;'>Staff Type Not Found</span>";
            }
        }
        $columns = $this->payroll_model->get_payroll_column_table();
        $enabled_headers = [];
        $enabled_columns =[];

        foreach($columns->payroll_column as $col){
            if($col->monthly_structure == 1){
                $enabled_headers[] = $col->display_name;
                $enabled_columns[] = $col->column_name;
            }
        }

        foreach($enabled_columns as $index => $column){
            if($column == 'monthly_basic_salary'){
                $enabled_columns[$index] = 'basic';
            }
            if($column == 'staff_hra'){
                $enabled_columns[$index] = 'hra';
            }
        }

        $enabled_headers[] = 'Total Earnings';
        $enabled_headers[] = 'Total Deduct';
        $enabled_headers[] = 'Net Pay';

        $enabled_columns[] = 'total_earnings';
        $enabled_columns[] = 'total_deductions';
        $enabled_columns[] = 'net_pay';

        $paisaSetting = $this->settings->getSetting("include_paisa_value_in_payroll_reports");

        foreach ($data['reports'] as &$report) {
            $total_earnings = floatval($report['total_earnings']);
            $total_deductions = floatval($report['total_deductions']);
            if (empty($paisaSetting) || $paisaSetting == 0) {
                // Round before calculating net pay
                $total_earnings = round($total_earnings, 0);
                $total_deductions = round($total_deductions, 0);
                $net_pay = $total_earnings - $total_deductions;

                $report['total_earnings'] = $total_earnings;
                $report['total_deductions'] = $total_deductions;
                $report['net_pay'] = round($net_pay, 0);
            } else {
                // Keep 2 decimals as float, then format for display
                $total_earnings = round($total_earnings, 2);
                $total_deductions = round($total_deductions, 2);
                $net_pay = $total_earnings - $total_deductions;

                $report['total_earnings'] = number_format($total_earnings, 2, '.', '');
                $report['total_deductions'] = number_format($total_deductions, 2, '.', '');
                $report['net_pay'] = number_format($net_pay, 2, '.', '');
            }
        }
        // foreach ($data['reports'] as &$report) {
        //     $total_earnings = floatval($report['total_earnings']);
        //     $total_deductions = floatval($report['total_deductions']);
        //     $net_pay = $total_earnings - $total_deductions;

        //     $report['net_pay'] = $net_pay;
        // }
        $response = [
            'reports' => $data['reports'],
            'enabled_headers' => $enabled_headers,
            'enabled_columns' => $enabled_columns
        ];

        echo json_encode($response);
    }


    public function tds_reports($select = '1') {
        if (!$this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')) {
            redirect('dashboard', 'refresh');
        }
        $data['selected_schedule'] = $select;
        $data['schedules'] = $this->payroll_model->get_schedules();
        $data['tds_reports'] = $this->payroll_model->get_tds_reports($select);
        //echo '<pre>'; print_r($data['reports']); die();
        $data['main_content'] = 'management/payroll/tds_reports';
        $this->load->view('inc/template', $data);
    }

    public function pf_reports($select = null) {
        if (!$this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')) {
            redirect('dashboard', 'refresh');
        }
        $data['schedules'] = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $current_month = date('m');
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $f_year_split = explode('-', $financial_year->f_year);
            $start_year = trim($f_year_split[0]);
            $end_year = trim($f_year_split[1]);
            if ($current_year == $start_year && $current_month >= 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
            elseif ($current_year == $end_year && $current_month < 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
        }
        if ($selected_financial_year === null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }

        $data['selected_financial_year'] = $selected_financial_year;
        if (empty($select)) {
            $startOfMonth = date('Y-m-01');
            $endOfMonth = date('Y-m-t');

            // Default selection logic for the current month
            foreach ($data['schedules'] as $schedule) {
                if (($schedule['start_date'] >= $startOfMonth && $schedule['start_date'] <= $endOfMonth) ||
                    ($schedule['end_date'] >= $startOfMonth && $schedule['end_date'] <= $endOfMonth)) {
                    $select = $schedule['id'];
                    break;
                }
            }
        }
        $data['selected_schedule'] = $select ?? '1';
        $data['title'] = 'Staff PF Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/pf_reports';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_pf_reports_ajax() {
        $selected_schedule = $this->input->post('selected_schedule');
        $pf_reports = $this->payroll_model->get_pf_reports($selected_schedule);

        echo json_encode($pf_reports);
    }

    public function pt_reports($select=null) {
        if (!$this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')) {
            redirect('dashboard', 'refresh');
        }
        $data['schedules'] = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $current_month = date('m');
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $f_year_split = explode('-', $financial_year->f_year);
            $start_year = trim($f_year_split[0]);
            $end_year = trim($f_year_split[1]);
            if ($current_year == $start_year && $current_month >= 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
            elseif ($current_year == $end_year && $current_month < 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
        }
        if ($selected_financial_year === null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }

        $data['selected_financial_year'] = $selected_financial_year;
        if (empty($select)) {
            $startOfMonth = date('Y-m-01');
            $endOfMonth = date('Y-m-t');

            // Default selection logic for the current month
            foreach ($data['schedules'] as $schedule) {
                if (($schedule['start_date'] >= $startOfMonth && $schedule['start_date'] <= $endOfMonth) ||
                    ($schedule['end_date'] >= $startOfMonth && $schedule['end_date'] <= $endOfMonth)) {

                    $select = $schedule['id'];
                    break;
                }
            }
        }
        $data['selected_schedule'] = $select ?? '1';

        $data['title'] = 'Staff PT Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/pt_reports';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_pt_reports() {
        $selected_schedule = $this->input->post('selected_schedule');
        $schedule_year = $this->input->post('schedule_year');
        $pt_reports = $this->payroll_model->get_pt_reports($selected_schedule);
        echo json_encode($pt_reports);
    }

    public function esi_reports()
    {
        if (!$this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')) {
            redirect('dashboard', 'refresh');
        }
        $data['schedules'] = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $current_month = date('n');
        $current_date = date('Y-m-d');
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $start_date = date('Y-m-d', strtotime($financial_year->from_date));
            $start_year = date('Y', strtotime($start_date));
            $start_month = date('n', strtotime($start_date));

            if ($current_month >= 4) {
                if ($current_date >= $start_date && $current_year == $start_year) {
                    $selected_financial_year = $financial_year->id;
                    break;
                }
            } else {
                if ($current_date >= $start_date && ($current_year - 1) == $start_year) {
                    $selected_financial_year = $financial_year->id;
                    break;
                }
            }
        }
        if ($selected_financial_year === null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }

        $data['selected_financial_year'] = $selected_financial_year;
        $data['title'] = 'Staff ESI Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/esi_reports';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_esi_reports_ajax() {
        $selected_schedule = $this->input->post('selected_schedule');
        $esi_reports = $this->payroll_model->get_esi_reports($selected_schedule);
        echo json_encode($esi_reports);
    }


    public function other_deduction_report($select=null) {
        if (!$this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')) {
            redirect('dashboard', 'refresh');
        }
        $data['schedules'] = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $current_month = date('m');
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $f_year_split = explode('-', $financial_year->f_year);
            $start_year = trim($f_year_split[0]);
            $end_year = trim($f_year_split[1]);
            if ($current_year == $start_year && $current_month >= 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
            elseif ($current_year == $end_year && $current_month < 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
        }
        if ($selected_financial_year === null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }

        $data['selected_financial_year'] = $selected_financial_year;
        if (empty($select)) {
            $startOfMonth = date('Y-m-01');
            $endOfMonth = date('Y-m-t');

            // Default selection logic for the current month
            foreach ($data['schedules'] as $schedule) {
                if (($schedule['start_date'] >= $startOfMonth && $schedule['start_date'] <= $endOfMonth) ||
                    ($schedule['end_date'] >= $startOfMonth && $schedule['end_date'] <= $endOfMonth)) {

                    $select = $schedule['id'];
                    break;
                }
            }
        }
        $data['selected_schedule'] = $select ?? '1';
        $data['title'] = 'Other Deductions Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/other_deduction_report';
        }
        $this->load->view('inc/template', $data);
    }

    public function other_deduction_report_ajax() {
        $selected_schedule = $this->input->post('selected_schedule');
        $data['other_deduction_reports'] = $this->payroll_model->get_other_deduction_reports($selected_schedule);

        echo json_encode($data['other_deduction_reports']);
    }

    public function vpf_reports($select = '1')
    {
        if (!$this->authorization->isAuthorized('PAYROLL.VIEW_REPORTS')) {
            redirect('dashboard', 'refresh');
        }
        $data['selected_schedule'] = $select;
        $data['schedules'] = $this->payroll_model->get_schedules();
        $data['vpf_reports'] = $this->payroll_model->get_reports($select);
        //echo '<pre>'; print_r($data['reports']); die();
        $data['main_content'] = 'management/payroll/vpf_reports';
        $this->load->view('inc/template', $data);
    }

    public function view_payroll_schedules() {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['selected_financial_year'] = '';
        $currentDate = date('Y-m-d');
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        foreach ($data['financial_year'] as $year) {
            $fromDate = strtotime($year->from_date);
            $nextYearStartDate = date('Y-04-01', strtotime('+1 year', $fromDate));

            if ($currentDate >= $year->from_date && $currentDate < $nextYearStartDate) {
                $data['selected_financial_year'] = $year->id;
                break;
            }
        }
        // $data['schedules'] = $this->payroll_model->get_all_schedules();
        // echo '<pre>'; print_r($data['financial_year']); die();
        $data['title'] = 'Payroll Schedules';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/view_schedules';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_schedules_financial_year_wise(){
        $financial_year = $_POST['selected_financial_year'];
        $result = $this->payroll_model->get_schedules_financial_year_wise($financial_year);
        // echo '<pre>'; print_r($result); die();
        echo json_encode($result);
    }

    public function view_financial_years() {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['schedules'] = $this->payroll_model->get_all_f_years();
        $data['title'] = 'Manage Financial Years';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/view_f_years';
        }
        $this->load->view('inc/template', $data);

    }

    public function increments()
    {

        //$data['$3'] = $this->$4->$5();get_all_staff_data
        $stfData = $this->payroll_model->get_all_staff_data();
        $data['get_all_staff_data'] = array();
        foreach ($stfData as $k => $val) {
            $data['get_all_staff_data'][$val->staff_id] = $val;
        }
        $data['schedules'] = $this->payroll_model->get_schedules();
        //  echo '<pre>'; print_r($data); die();
        $data['main_content'] = 'management/payroll/increments';
        $this->load->view('inc/template', $data);
    }

    public function save_increments()
    {
        if (isset($_FILES['letter'])) {
            $filepath = $this->s3FileUpload($_FILES['letter']);
            // echo $filepath; die();
            $result = $this->payroll_model->save_increments($filepath['file_name']);
        } else {

            $result =  $this->payroll_model->save_increments();
        }

        //echo '<pre>'; print_r(save_incrementsdata['schedules']); die();
        if ($result)
            $this->session->set_flashdata('flashSuccess', 'Increment Added Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong.');
        redirect("management/payroll/");
    }


    public function s3FileUpload($file)
    {
        if ($file['tmp_name'] == '' || $file['name'] == '') {
            // echo "pop"; die();
            return ['status' => 'empty', 'file_name' => ''];
        }
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'payroll_increments');
    }


    // public function arrears()
    // {
    //     $data['staffData'] = $this->payroll_model->get_staff_names();
    //     $data['main_content'] = 'management/payroll/arrears';
    //     $this->load->view('inc/template', $data);
    //     //echo '<pre>'; print_r(arrearsdata['schedules']); die();
    // }

    public function save_financial_years() {
        $input = $this->input->post();
        $result = $this->payroll_model->save_financial_years($input);
        echo $result;
    }

    public function settings(){
        $payroll_columns = $this->payroll_model->get_fields_columns_payroll();
        $enabled_fields = $this->cmodel->getEnabledValues('payroll');
        $eFields = json_decode($enabled_fields);
        $enabledOptions = array();
        foreach ($payroll_columns as $columns) {
            $found = 0;
            if (!empty($eFields)) {
                foreach ($eFields as $eColumn) {
                    if ($columns ==  $eColumn) {
                        $found = 1;
                        break;
                    }
                }
            }
            if ($found) {
                $enabledOptions [] =$columns;
            } else {
                $disabledOptions [] = $columns;
            }
            
        }
        $data['payroll_columns'] = $disabledOptions;
        $data['selected_columns'] = $enabledOptions;
        $data['main_content'] = 'management/payroll/settings';
        $this->load->view('inc/template', $data);
    }

    public function settings_config_insert()
    {
        $new_module_string = json_encode($_POST['new_modules']);
        $result = $this->cmodel->upsertToConfig($_POST['name'],$new_module_string, 'multiple'); 
        if($result){
          redirect('management/payroll/settings');
        }else{
          redirect('management/payroll/settings');
        }
    }

    public function payroll_template(){
        $this->config->load('form_elements');
        $data['employmentType'] = $this->config->item('employmentType');
        $data['payroll_template'] = $this->payroll_model->get_payroll_template();
        $data['main_content']    = 'management/payroll/payroll_template';
        $this->load->view('inc/template', $data);
    }

    public function insert_payroll_template() {
        $result=$this->payroll_model->insert_payroll_template();
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Added Successful');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong');
        }
        redirect('management\payroll\payroll_template');
    }

    public function slab() {
        $data['slabs'] = $this->payroll_model->get_payroll_settings();
        // $data['payroll_fields'] = $this->cmodel->getEnabledValues('payroll');
        $data['main_content'] = 'management/payroll/slab';
        $this->load->view('inc/template', $data);
    }


    public function loan_index(){
        $data['staff_loan_data'] = $this->payroll_model->get_staff_loanData();
        $data['main_content'] = 'management/payroll/loan_index';
        $this->load->view('inc/template', $data);
    }

    public function loan_schedule($loan_amount_id, $staff_id) {
        $data['loan_amount_id'] = $loan_amount_id;
        $data['staff_id'] = $staff_id;
        $data['staff_name'] = $this->payroll_model->getStaffName_id($data['staff_id']);
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
        $data['main_content'] = 'management/payroll/loan';
        $this->load->view('inc/template', $data);
    }

    // public function loan_schedule($staff_id)
    // {
    //     $data['staff_id'] = $staff_id;
    //     $data['staff_name'] = $this->payroll_model->getStaffName_id($data['staff_id']);
    //     $data['staff_list'] = $this->payroll_model->get_staff_list();
    //     $data['financial_year'] = $this->payroll_model->get_financial_year();
    //     $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    //     $data['main_content'] = 'management/payroll/loan';
    //     $this->load->view('inc/template', $data);
    // }

    public function loan_edit($staff_id){
        $data['staff_id'] = $staff_id;
        $data['staff_name'] = $this->payroll_model->getStaffName_id($data['staff_id']);
        $data['staff_list'] = $this->payroll_model->get_staff_list();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
        $data['staff_loan_edit_data'] = $this->payroll_model->get_staff_loan_edit_data($staff_id
        );
        $data['main_content'] = 'management/payroll/loan_edit';
        $this->load->view('inc/template', $data);
    }

    public function save_loan() {
        $result = $this->payroll_model->save_loan_structure();
        if ($result)
            $this->session->set_flashdata('flashSuccess', 'Updated Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong.');
        redirect("management/payroll/loan_index");
    }

    public function staff_loan_amount_schedule() {
        $schedule_year = $_POST['schedule_year'];
        $result = $this->payroll_model->staff_loan_amount_schedule($schedule_year);
        echo json_encode($result);
    }

    public function loan_amount_table_diplay($staff_id) {
        $staff_id = $_POST['staff_id'];
        $result = $this->payroll_model->get_loan_data_Template($staff_id);
        echo json_encode($result);
    }

    public function save_settings(){
        $result = $this->payroll_model->save_settings();
        sleep(1);
        redirect("management/payroll/slab");
    }

    public function updatesettings(){
        $data['settings'] = $this->payroll_model->get_payroll_settings();
        $data['main_content'] = 'management/payroll/edit_settings';
        $this->load->view('inc/template', $data);
    }

    public function save_updated_settings(){
        $result = $this->payroll_model->save_updated_settings();
        if ($result)
            $this->session->set_flashdata('flashSuccess', 'Updated Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong.');
        redirect("management/payroll/slab");
    }

    public  function update_edited_salary() {
        $result = $this->payroll_model->update_edited_salary();
        if ($result)
        redirect("management/payroll/view_payslip/" . $_POST['staff_id'] . '/' . $_POST['schedule_id']);

        else
            $this->session->set_flashdata('flashError', 'Something went wrong.');
        redirect("management/payroll/view_payslip/" . $_POST['staff_id'] . '/' . $_POST['schedule_id']);
    }

    public function payslipPdf($html) {
        $school = CONFIG_ENV['main_folder'];
        $path = $school . '/payslips/' . uniqid() . '-' . time() . ".pdf";
        $bucket = $this->config->item('s3_bucket');

        $insert_id = $this->payroll_model->addPayslipPdfPath($path);
        //echo $status; die();
        $curl = curl_init();

        $html = str_replace('+', '%252B', $html);
        $postData = urlencode($html);

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url() . 'Callback_Controller/payslipPdfStatus';

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&data=" . $postData . "&return_url=" . $return_url,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            echo 0;
        } else {
            echo 1;
            // echo $response;
        }
        return $insert_id;
    }

    public function download_as_pdf($staff_id, $schedule_id) {
        $path = $this->payroll_model->getFilePath($staff_id, $schedule_id);
		$url = $this->filemanager->getFilePath($path);
		$data = file_get_contents($url);
		$this->load->helper('download');
        force_download('payslip.pdf', $data, TRUE);
    }

    public function annual_summary_report(){
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $current_month = date('n');
        $current_date = date('Y-m-d');
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $start_date = date('Y-m-d', strtotime($financial_year->from_date));
            $start_year = date('Y', strtotime($start_date));
            $start_month = date('n', strtotime($start_date));

            if ($current_month >= 4) {
                if ($current_date >= $start_date && $current_year == $start_year) {
                    $selected_financial_year = $financial_year->id;
                    break;
                }
            } else {
                if ($current_date >= $start_date && ($current_year - 1) == $start_year) {
                    $selected_financial_year = $financial_year->id;
                    break;
                }
            }
        }
        if ($selected_financial_year === null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }

        $data['selected_financial_year'] = $selected_financial_year;
        $data['title'] = 'Annual Summary Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/annual_summary_report';
        }
        $this->load->view('inc/template', $data);
    }

    public function getAnnualSummaryReport(){
        $financialYear = $_POST['financialYear'];
        $columns = $this->payroll_model->get_payroll_column_table();
        $enabledHeaders = [];
        $enabledColumns =[];

        foreach($columns->payroll_column as $col){
            if($col->monthly_structure == 1){
                $enabledHeaders[] = $col->display_name;
                $enabledColumns[] = $col->column_name;
            }
        }

        foreach($enabledColumns as $index => $column){
            if($column == 'monthly_basic_salary'){
                $enabledColumns[$index] = 'basic';
            }
            if($column == 'staff_hra'){
                $enabledColumns[$index] = 'hra';
            }
            if($column == 'staff_da'){
                $enabledColumns[$index] = 'da';
            }
        }
        $data['annualSummaryData']   =  $this->payroll_model->annual_summary_report($financialYear, $enabledColumns);
        if(empty($data['annualSummaryData'])){
            echo json_encode([]);
            return;
        }
        $returnData[] = [
            'headers' => $enabledHeaders,
            'columns' => $enabledColumns,
            'data' => $data['annualSummaryData'],
        ];
        echo json_encode($returnData);
    }

    public function staff_summary_report($fyear = 0){
        $data['selected_fyear'] = $fyear; 
        $data['f_years']  = $this->payroll_model->getFyears();
        $current_year = date('Y');
        $current_month = date('m');
        $selected_financial_year = null;

        if ($fyear > 0) {
            $selected_financial_year = $fyear;
        } else {
            foreach ($data['f_years'] as $financial_year) {
                $f_year_split = explode('-', $financial_year['f_year']);
                $start_year = trim($f_year_split[0]);
                $end_year = '20' . trim($f_year_split[1]);

                if ($current_year == $start_year && $current_month >= 4) {
                    $selected_financial_year = $financial_year['id'];
                    $fyear = $financial_year['id'];
                    break;
                } elseif ($current_year == $end_year && $current_month < 4) {
                    $selected_financial_year = $financial_year['id'];
                    $fyear = $financial_year['id'];
                    break;
                }
            }

            if ($selected_financial_year === null && !empty($data['f_years'])) {
                $selected_financial_year = $data['f_years'][0]['id'];
            }
        }

        $data['selected_financial_year'] = $selected_financial_year;
        $data['reports']   =  $this->payroll_model->staff_Summary_report($fyear);
        // echo '<pre>'; print_r($data['f_years']); die();
        $data['title'] = 'Staff Summary Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/staff_summary_report';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_slab_staffwise_details() {
        $staffId = $_POST['staffId'];
        $result = $this->payroll_model->get_staff_slab($staffId);
        echo json_encode($result);
    }

    public function get_slab_settings() {
        $slabs = $_POST['slabs'];
        $result = $this->payroll_model->get_slab_settings_details($slabs);
        echo json_encode($result);
    }

    public function disbursement(){
        $schedules = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        // echo "<pre>";print_r($data['financial_year']);die();
        $current_year = date('Y');
        $current_month = date('m');
        $present_month = '';
        foreach ($schedules as $key => &$val) {
            if (date('m',strtotime($val['start_date'])) == date('m', strtotime(date('Y-m')." -1 month"))) {
                $val['scMonth'] = 'Selected';
                $present_month = $val['id'];
            }else{
                $val['scMonth'] = 'Not Selected';
            }
        }
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $f_year_split = explode('-', $financial_year->f_year);
            $start_year = trim($f_year_split[0]);
            $end_year = trim($f_year_split[1]);
            if ($current_year == $start_year && $current_month >= 4) {
                $selected_financial_year = $financial_year->id;
                break; // Stop after finding the matching financial year
            }
            // If the current year is the end year of `f_year` and we are before April (end of financial year)
            elseif ($current_year == $end_year && $current_month < 4) {
                $selected_financial_year = $financial_year->id;
                break; // Stop after finding the matching financial year
            }
        }
        if ($selected_financial_year === null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }

        $data['selected_financial_year'] = $selected_financial_year;
        $data['schedules'] = $schedules;
        $data['disbursements'] = $this->payroll_model->get_disbursement_generated_staff($present_month);
        $data['main_content'] = 'management/payroll/disbursement';
        $this->load->view('inc/template', $data);

    }

    public function onchange_disbursement_schedule(){
        $selected_schedule = $_POST['selected_schedule'];
        $result = $this->payroll_model->get_disbursement_generated_staff($selected_schedule);
        echo json_encode($result);
    }


    public function add_disbursement(){
        $selected_schedule = $this->input->post('selected_schedule'); 
        // echo "<pre>"; print_r($staff_ids); die();
        $data['selected_schedule'] = $selected_schedule;
        $data['scheduled_data'] = $this->payroll_model->getScheduleData($selected_schedule);
        $data['salary_data'] = $this->payroll_model->get_payslip_generated_staff(0, $selected_schedule);
        // echo "<pre>"; print_r($data['salary_data']); die();
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['main_content'] = 'management/payroll/disbursement_add';
        $this->load->view('inc/template', $data);
        
    }

    public function edit_disbursement($disbursementId, $selected_schedule){
        $data['selected_schedule'] = $selected_schedule;
        $data['disbursement_id'] = $disbursementId;
        $data['scheduled_data'] = $this->payroll_model->getScheduleData($selected_schedule);
        $edit_data = $this->payroll_model->edit_disbursement_staff_details($disbursementId, $selected_schedule);

        $staff_data = $this->payroll_model->get_payslip_generated_staff(0, $selected_schedule);
        $data['edit_data'] = $edit_data;
        // echo "<pre>"; print_r($data['edit_data']);die();
        $data['staff_edit_data'] = array_merge($edit_data->staff_list, $staff_data);
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['main_content'] = 'management/payroll/disbursement_edit';
        $this->load->view('inc/template', $data);
    }

    public function disbursement_slip(){
        $input = $this->input->post();
        // echo "<pre>"; print_r($input);die();
        $arg= '';
        foreach($input['payslip_id'] as $key => $val) {
            $arg .= $val. '_';
        }
        $last_id = $this->payroll_model->disbursement_slip($input);
        if ($last_id) {
            redirect("management/payroll/view_disbursement/".$last_id.'/'.$input['schedule_id'].'/'.$arg);
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect('management/payroll/add_disbursement');
        }
    }

    public function view_disbursement($last_id, $schedulesId,$s_id_str=''){
        $data['s_id']=explode('_',$s_id_str);
        $data['selected_schedule'] = $schedulesId;
        $data['disbursement_id'] = $last_id;
        $data['disbursement_data'] = $this->payroll_model->get_disbursement_staff($last_id, $schedulesId);
        // echo "<pre>"; print_r($data['disbursement_data']); die();
        $data['main_content'] = 'management/payroll/disbursement_view';
        $this->load->view('inc/template', $data);
    }

    public function get_staff_type_details(){
        $staff_type = $_POST['staff_type'];
        $selected_schedule = $_POST['selected_schedule'];
        $result = $this->payroll_model->get_payslip_generated_staff($staff_type, $selected_schedule);
        echo json_encode($result);
    }

    public function disbursement_slip_publish(){
        $input = $this->input->post();
        $result = $this->payroll_model->disbursement_slip_publish_byId($input);
        $disbursement_notify = $this->settings->getSetting('payroll_disbursement_notification');
        if ($result) {
            if($disbursement_notify){
                $datge_year=date('F Y');
                $input_notification = array(
                    'title' => 'Payroll',
                    'message' => "Your Payslip is now available",
                    'source' => 'Your source',
                    'mode' => 'notification', // or 'notification_sms' if you want to send both notification and SMS
                    'send_to' => 'Staff', // Send notification only to staff
                    'staff_ids' => $input['staff_ids'] // An array of staff IDs to receive the notification
                );
                $this->load->helper('texting_helper');
                // Call the sendText function
                // echo '<pre>'; print_r($input_notification); die();
                $response = sendText($input_notification);     
            }
            redirect("management/payroll/print_disbursement/".$input['disbursement_id'].'/'.$input['schedule_id']);
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect('management/payroll/add_disbursement');
        }
    }

    public function print_disbursement($disbursementId, $schedulesId){
        $data['disbursementId'] = $disbursementId;
        $data['selected_schedule'] = $schedulesId;
        $selected_schedule_name = $this->payroll_model->edit_schedules_rowid($schedulesId);
        $data['selected_schedule_name'] = $selected_schedule_name->schedule_name;
        $data['disbursement_data'] = $this->payroll_model->get_disbursement_staff($disbursementId, $schedulesId);
        $data['main_content'] = 'management/payroll/disbursement_print';
        $this->load->view('inc/template', $data);
    }

    public function delete_disbursement($disbursementId){
        $result = $this->payroll_model->delete_disbursement_details($disbursementId);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Deleted Successfully');
            redirect("management/payroll/disbursement");
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect('management/payroll/disbursement');
        }
    }

    public function update_disbursement_slip(){
        $input = $this->input->post();
        $last_id = $this->payroll_model->update_disbursement_slip($input);
        if ($last_id) {
            redirect("management/payroll/view_disbursement/".$input['disbursement_id'].'/'.$input['schedule_id']);
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
            redirect('management/payroll/add_disbursement');
        }
    }

    public function import_excelto_erp() {
        $schedules = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $data['main_content'] = 'management/payroll/import_excelto_erp';
        $this->load->view('inc/template', $data);
    }

    public function get_month_details_loan() {
        $schedule_year = $_POST['schedule_year'];
        $staff_id = $_POST['staff_id'];
        $result = $this->payroll_model->get_month_details_loan_data($schedule_year, $staff_id);
        echo json_encode($result);
    }

    public function save_staff_loan_amount_schedule() {
        $loan_amount_id = $_POST['loan_amount_id'];
        $schedule_month = $_POST['schedule_month'];
        $deduction_amount = $_POST['deduction_amount'];
        echo $this->payroll_model->save_staff_loan_amount_schedule($loan_amount_id, $schedule_month, $deduction_amount);
    }

    public function save_total_loan_amount() {
        $result = $this->payroll_model->save_total_loan_amount();
        if ($result) {
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
        }
        redirect('management/payroll/loan_index');
    }

    public function get_loan_repayment_details_by_id() {
        $loan_amount_id = $_POST['loan_amount_id'];
        $result = $this->payroll_model->get_loan_repayment_details_by_id($loan_amount_id);
        echo json_encode($result);
    }

    private $csvarrays = array(
        'staffid',
        'staffname',
        'schedule_id',
        'schedule_month',
        'tds',
        'reimbursement',
    );


    public function download_csvfor_tds_reimbusment() {
        $schedule_month = $this->input->post('schedule_month');
        $check_payslip_data = $this->payroll_model->check_sechdule_month_data($schedule_month);
        if ($check_payslip_data) {
            $get_staff_data = $this->payroll_model->get_all_staff_data_payroll_csv();
            $get_schedule_month = $this->payroll_model->get_schedule_month_csv($schedule_month);
        }else{
            $this->session->set_flashdata('flashInfo', 'Payslip genereted this month');
            redirect('management/payroll/import_excelto_erp');
        }

        $csvHeaderarrays[] = array('employee_id', 'staff_id','schedule_id','staff_name','schedule_month','tds','reimbursement');
        $csvarrays = [];
        foreach ($get_staff_data as $val) {
            $csvarrays[] = array($val->employee_id, $val->staff_id,$schedule_month,$get_schedule_month->schedule_name,$val->staff_name,'','');
        }
        $datamerge = array_merge($csvHeaderarrays, $csvarrays);
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="payroll_data.csv"');
        $output = fopen('php://output', 'w');
        foreach ($datamerge as $row) {
            fputcsv($output, $row);
        }
        fclose($output);
        exit;
    }

    public function upload($filename = '', $upload_path) {
        $config['upload_path'] = $upload_path;
        $config['allowed_types'] = 'csv|CSV';
        $config['remove_spaces'] = true;
        $config['overwrite'] = false;
        
        $this->load->library('upload', $config);
        $this->upload->initialize($config);
        if (!$this->upload->do_upload($filename)) {
            $error = array('status' => 'error', 'data' => $this ->upload-> display_errors());
            $this ->session ->set_flashdata('flashError', 'Failed to upload - ' . $filename);
            return $error;
        } else {
            $image = $this->upload -> data();
            $success = array('status' => 'success', 'data' => $image);
            return $success;
        }

    }

    public function upload_csvfor_tds_reimbusment() {
        $this->load->library('csvimport'); 
        $creDir = 'uploads/payroll';
        if (!is_dir($creDir)) {
            mkdir($creDir, 0777, TRUE);
        }

        if ($_FILES['payroll_data']['name'] != "") {
            $ret_val = $this->upload('payroll_data', $creDir);
            if ($ret_val['status'] == 'success') {
                $file_data = $ret_val['data'];
            } else {
                $file_data = $ret_val['data'];
            }
        }else{
            $file_data="";
        }
        $file_path = 'uploads/payroll/'.$file_data['file_name'];
        if ($this->csvimport->get_array($file_path)) {
            $data_arr = $this->csvimport->get_array($file_path);
            foreach ($data_arr as $key => $std) {
                $data[] = array( 
                    'staff_id' => $std['staff_id'], 
                    'schedule_id' => $std['schedule_id'],
                    'tds' => $std['tds'],
                    'reimbursement' => $std['reimbursement'],
                );

            }
            $result = $this->db->insert_batch('new_payroll_tds_reimbursement_data',$data);
            if($result){ 
                $this->session->set_flashdata('flashSuccess', 'Data Upload Successful');
            } else{
                $this->session->set_flashdata('flashError', 'Something Went Wrong');
            }
            redirect('management/payroll/import_excelto_erp');
        }
    }

    public function download_payslip($payslipId, $month_name='') {
        $link = $this->payroll_model->get_payslip_pdf_path($payslipId);
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $filename = 'payslip_' . $month_name . '.pdf';
        $this->load->helper('download');
        force_download($filename, $data, TRUE); 
    }

    public function getHolidayCount(){
        $start_date = $_POST['startDate'];
        $end_date = $_POST['endDate'];
        $start_date_formatted = strtotime($start_date);
        $end_date_formatted = strtotime($end_date);
        $datediff = $end_date_formatted - $start_date_formatted;
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;
        $holidays = 0;
        echo $leaveDays - $holidays;
    }

    public function edit_schedules_id($id){
        $data['years'] = $this->payroll_model->get_fin_years();
        $data['schedules'] = $this->payroll_model->edit_schedules_rowid($id);
        $data['title'] = 'Edit Payroll Schedule';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/schedules_edit';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_financial_year_by_id(){
        $id = $_POST['id'];
        $result = $this->payroll_model->get_financial_yearby_id($id);
       // echo "<pre>"; print_r($result); die();
        echo json_encode($result);
    }

    public function delete_schedules_id(){
        $id = $_POST['id'];
        $result = $this->payroll_model->delete_schedules_db($id);
        echo json_encode($result);
    }

    public function update_payroll_schedule(){
        $result = $this->payroll_model->update_payroll_schedule();
        // if ($result)
        //     $this->session->set_flashdata('flashSuccess', 'Payroll Schedule is saved.');
        // else
        //     $this->session->set_flashdata('flashError', 'Something went wrong.');
        sleep(1);
        redirect("management/payroll/view_payroll_schedules");
    }

    public function view_slab_byId() {
        $id = $_POST['id'];
        $result = $this->payroll_model->view_edit_slab_byId($id);
        // echo "<pre>"; print_r($result); die();
        echo json_encode($result);
    }

    public function edit_slab_byId(){
        $id = $_POST['id'];
        $result = $this->payroll_model->view_edit_slab_byId($id);
        echo json_encode($result);
    }

    public function delete_slab_id(){
        $id = $_POST['id'];
        $success = $this->payroll_model->delete_slab_id($id); 
        echo json_encode($success);
    }

    public function update_slab_byId(){
     // echo "<pre>"; print_r($_POST); die();
        echo $this->payroll_model->update_slab_by_Id();
    }

    public function edit_fyear_byId(){
        $id = $_POST['id'];
        $result = $this->payroll_model->edit_fyear_byId($id);
        echo json_encode($result);
    }

    public function delete_fyear_id(){
        $id = $_POST['id'];
        $success = $this->payroll_model->delete_fyear_id($id); 
        echo json_encode($success);
    }

    public function update_payroll_submit(){
        $staff_visibility = $_POST['edit_staff_visibility'];
        $edit_financial_name = $_POST['edit_financial_name'];
        $edit_from_date = $_POST['edit_from_date'];
        $edit_to_date = $_POST['edit_to_date'];
        $edit_slab_id = $_POST['edit_slab_id'];
        $edit_auto_tds = $_POST['edit_auto_tds'];
        $result = $this->payroll_model->update_payroll_submit($staff_visibility, $edit_financial_name, $edit_from_date, $edit_to_date, $edit_slab_id, $edit_auto_tds);
        echo json_encode($result);
    }

    public function _create_template_payslip($payslip, $template) {
        $paisaSetting = $this->settings->getSetting("include_paisa_value_in_payroll_reports");
        if(empty($paisaSetting) || $paisaSetting == 0){
            foreach ($payslip as $key => $val) {
                if (is_numeric($val) && !in_array($key, ['account_number', 'lop', 'lop_cal'])) {
                    $payslip[$key] = round($val, 0);
                }
            }
        } else {
            foreach ($payslip as $key => $val) {
                if (is_numeric($val) && !in_array($key, ['account_number', 'lop', 'lop_cal'])) {
                    $payslip[$key] = number_format($val, 2, '.', '');
                }
            }
        }
        
        $monthlyNetWithoutPF= 0;
        $monthlyNet= 0;
        $net_pay_amount= 0;
        if(!empty($payslip['monthly_net_without_pf'])){
            $monthlyNetWithoutPF = $payslip['monthly_net_without_pf'];
        }
        if(!empty($payslip['monthly_net'])){
            $monthlyNet = $payslip['monthly_net'];
        }
        if(!empty($payslip['net_pay_amount'])){
            $net_pay_amount = $payslip['net_pay_amount'];
        }
        $amountInWords = $this->getIndianCurrency($monthlyNet);
        $amountInWordsWithoutPF = $this->getIndianCurrency($monthlyNetWithoutPF);
        $net_pay_amount = $this->getIndianCurrency($net_pay_amount);
        $template = str_replace('%%in_words1%%', $amountInWordsWithoutPF, $template);
        $template = str_replace('%%in_words%%', $amountInWords, $template);
        $template = str_replace('%%net_pay_in_words%%', $net_pay_amount, $template);
        foreach ($payslip as $mapstring => $val) {
            $maping = '%%'.$mapstring.'%%';
            $template = str_replace($maping, $val, $template);
        }
        $template = str_replace('%%', '-', $template);
        return $template;
    }

    public function generate_pdf_payroll(){
        $staffId = $this->input->post('staffId');
        $selected_month = $this->input->post('selected_month');
        $template = $this->payroll_model->get_email_template_for_payslip();
        if($template){
            $data['payslip'] = $this->payroll_model->get_payslip_info($staffId, $selected_month);
            $pdf_html = $this->_create_template_payslip($data['payslip'],$template);
            $update =  $this->payroll_model->update_payroll_html_receipt($pdf_html, $staffId, $selected_month);
            $this->genearte_payslip_pdf($pdf_html, $staffId, $selected_month);
        }
    }

    public function getIndianCurrency(float $number) {
        $schoolName = $this->settings->getSetting('school_short_name');
        $decimal = round($number - ($no = floor($number)), 2) * 100;
        $hundred = null;
        $digits_length = strlen($no);
        $i = 0;
        $str = array();
        $words = array(0 => '', 1 => 'One', 2 => 'Two',
            3 => 'Three', 4 => 'Four', 5 => 'Five', 6 => 'Six',
            7 => 'Seven', 8 => 'Eight', 9 => 'Nine',
            10 => 'Ten', 11 => 'Eleven', 12 => 'Twelve',
            13 => 'Thirteen', 14 => 'Fourteen', 15 => 'Fifteen',
            16 => 'Sixteen', 17 => 'Seventeen', 18 => 'Eighteen',
            19 => 'Nineteen', 20 => 'Twenty', 30 => 'Thirty',
            40 => 'Forty', 50 => 'Fifty', 60 => 'Sixty',
            70 => 'Seventy', 80 => 'Eighty', 90 => 'Ninety');
        $digits = array('', 'Hundred','Thousand','Lakh', 'Crore');
        while( $i < $digits_length ) {
            $divider = ($i == 2) ? 10 : 100;
            $number = floor($no % $divider);
            $no = floor($no / $divider);
            $i += $divider == 10 ? 1 : 2;
            if ($number) {
                $plural = (($counter = count($str)) && $number > 9) ? '' : null;
                $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
                $str [] = ($number < 21) ? $words[$number].' '. $digits[$counter]. $plural.' '.$hundred:$words[floor($number / 10) * 10].' '.$words[$number % 10]. ' '.$digits[$counter].$plural.' '.$hundred;
            } else $str[] = null;
        }
        $Rupees = implode('', array_reverse($str));
        $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
        if ($schoolName === 'prarthana') {
            return 'Rupees ' . ( $Rupees ? $Rupees . 'Only ' : ' ') . $paise ;
        }
        return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise ;
    }

    public function get_loan_total_data(){
        $staff_id = $_POST['staff_id'];
        $success = $this->payroll_model->get_loan_total_data($staff_id); 
        echo json_encode($success);
    }

    // public function entitled_earned_salary($selected_month ='', $schedule_year = ''){
    //     if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
    //         redirect('dashboard', 'refresh');
    //     }
    //     $data['sStatus'] = $this->settings->getSetting('staff_status');
       
    //     $data['adSelected'] = '2';

    //     $stafType = $this->input->post('staff_type');
    //     if (!empty($stafType)) {
    //        $stafType =$stafType;
    //     }else{
    //         $stafType = 'all';
    //     }
    //     $data['get_staff_data'] = $this->payroll_model->get_all_staff_data_payroll($stafType, $data['adSelected']);
    //     // echo "<pre>"; print_r($data['get_staff_data']); die();
    //     $data['staff_type'] = $this->settings->getSetting('staff_type');
    //     $data['selected_staff_type'] = $stafType;
        
    //     $data['main_content'] = 'management/payroll/entitled_salary';
    //     $this->load->view('inc/template', $data);
    // }

    public function add_entitled_salary($staff_id){
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['staff_id'] = $staff_id;
        $data['staff_data'] = $this->payroll_model->getPayrollData($staff_id);
        // echo "<pre>"; print_r($data['staff_data']); die();
        $data['scheduled_data'] = $this->payroll_model->getScheduleData(1);
        $data['main_content'] = 'management/payroll/create_entitled_amount';
        $this->load->view('inc/template', $data);
    }

    public function save_entitled_amount(){
        $result = $this->payroll_model->save_entitled_amount();
        redirect("management/payroll/entitled_earned_salary");
    }

    public function edit_entitled_salary($staff_id){
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['staff_id'] = $staff_id;
        $data['staff_data'] = $this->payroll_model->getPayrollData_entitled($staff_id);
        // echo "<pre>"; print_r($data['staff_data']); die();
        $data['scheduled_data'] = $this->payroll_model->getScheduleData(1);
        $data['main_content'] = 'management/payroll/edit_entitled_amount';
        $this->load->view('inc/template', $data);
    }

    public function mass_schedulePayroll($selected_month ='', $schedule_year = '') {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $this->config->load('form_elements');
        $data['employmentType'] = $this->config->item('employmentType');
        $data['staff_data'] = $this->payroll_model->getPayrollData_mass_generation();
        $data['staff_list'] = $this->payroll_model->get_approved_staff_list();

        $data['sStatus'] = $this->settings->getSetting('staff_status');

        $data['adSelected'] = '2';

        $stafType = $this->input->post('staff_type');
        if (!empty($stafType)) {
            $stafType =$stafType;
        }else{
            $stafType='all';
        }

        $schedules = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $present_month = (empty($schedules))?'':$schedules[0]['id'];

        foreach ($schedules as $key => &$val) {
            $val['scMonth'] = 'Not Selected';
            if (date('m',strtotime($val['start_date'])) == date('m', strtotime(date('Y-m')." -1 month"))) {
                $val['scMonth'] = 'Selected';
                $present_month = $val['id'];
            }
        }
        if (!empty($selected_month)) {
            $data['selected_schedule'] = $selected_month;
        }else{
            $data['selected_schedule'] = $present_month;
        }
        
        $schedule_year = (empty($data['financial_year']))?'':$data['financial_year'][0]->id;
        $currentYear = date('Y');
        $currentMonth = date('n');
        foreach ($data['financial_year'] as $key => $val) {
            if ($currentMonth <= 3) {
                if (date('Y', strtotime($val->from_date)) == ($currentYear - 1)) {
                    $schedule_year = $val->id;
                    break;
                }
            } else {
                if (date('Y', strtotime($val->from_date)) == $currentYear) {
                    $schedule_year = $val->id;
                    break;
                }
            }
        }
        $data['schedules'] = $schedules;
        $data['schedule_year'] = $schedule_year;
        $data['templateData'] = $this->payroll_model->getTemplateData($present_month, $stafType, $data['adSelected']);
        // echo "<pre>"; print_r($data['sStatus']); die();
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['selected_staff_type'] = $stafType;
        $columns = $this->payroll_model->get_payroll_column_table();
        if(!empty($columns)){
            $data['main_content'] = 'management/payroll/mass_payslip_generation_new';
        }else{
            $data['main_content'] = 'management/payroll/mass_payslip_generation';
        }
        $this->load->view('inc/template', $data);
    }

    public function schedulePayroll_onchagne_mass_generation(){
        $selected_schedule = $_POST['selected_schedule'];
        $stafftypeId = $_POST['stafftypeId'];
        $staff_status = $_POST['staff_status'];
        $result = $this->payroll_model->getTemplateData_mass_generation($selected_schedule, $stafftypeId, $staff_status);
        echo json_encode($result);
    }

    public function edit_per_staff_paslip() {
        $selected_schedule = $_POST['selected_schedule'];
        $staff_id = $_POST['staff_id'];
        $result = $this->payroll_model->edit_per_staff_paslip($selected_schedule, $staff_id);
        echo json_encode($result);
    }

    public function mass_payroll_generate_cal(){
        $stafftypeId = $_POST['stafftypeId'];
        $staff_status = $_POST['staff_status'];
        $schedule_year = $_POST['schedule_year'];
        $selected_schedule = $_POST['selected_schedule'];
        $result = $this->payroll_model->get_all_stafff_generate_payslip_temp($stafftypeId, $staff_status, $schedule_year, $selected_schedule);
    }

    public function payslip_approval($selected_month ='', $schedule_year = '') {
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
        $this->config->load('form_elements');
        $data['employmentType'] = $this->config->item('employmentType');
        $data['sStatus'] = $this->settings->getSetting('staff_status');
        $data['adSelected'] = '2';
        $stafType = $this->input->post('staff_type');
        if (!empty($stafType)) {
            $stafType =$stafType;
        }else{
            $stafType='all';
        }

        $schedules = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $present_month = (empty($schedules))?'':$schedules[0]['id'];

        foreach ($schedules as $key => &$val) {
            $val['scMonth'] = 'Not Selected';
            if (date('m',strtotime($val['start_date'])) == date('m', strtotime(date('Y-m')." -1 month"))) {
                $val['scMonth'] = 'Selected';
                $present_month = $val['id'];
            }
        }
        if (!empty($selected_month)) {
            $data['selected_schedule'] = $selected_month;
        }else{
            $data['selected_schedule'] = $present_month;
        }
        
        $data['schedules'] = $schedules;
        $data['schedule_year'] = $schedule_year;

        $data['templateData'] = $this->payroll_model->getTemplateData_approval_list($present_month, $stafType, $data['adSelected'], $data['staffid']);
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['selected_staff_type'] = $stafType;
        
        $data['title'] = 'Payslips Approval';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/payslip_approval';
        }
        $this->load->view('inc/template', $data);
    }

    public function approval_view_per_staff_paslip(){
        $selected_schedule = $_POST['selected_schedule'];
        $staff_id = $_POST['staff_id'];
        $employmentType = $_POST['employmentType'];
        $data['result'] = $this->payroll_model->view_per_staff_paslip($selected_schedule, $staff_id);
        $columnArray = $this->payroll_model->get_payroll_column_table();
        $earnings = [];
        $deduction = [];
        if(!empty($columnArray)){
            $earnings = [];
            $deduction = [];
            foreach ($columnArray->payroll_column as $key => $val) {
                $isConsultantColumn = isset($val->is_consultant) && $val->is_consultant == '1';

                if ($val->type == 'earnings' && $val->monthly_structure == '1') {
                    if ($employmentType == 'Consultant') {
                        if ($isConsultantColumn) {
                            $earnings[] = $val;
                        }
                    } else {
                        if (!$isConsultantColumn) {
                            $earnings[] = $val;
                        }
                    }
                }

                if ($val->type == 'deduction' && $val->monthly_structure == '1') {
                    if ($employmentType == 'Consultant') {
                        if ($isConsultantColumn) {
                            $deduction[] = $val;
                        }
                    } else {
                        if (!$isConsultantColumn) {
                            $deduction[] = $val;
                        }
                    }
                }
            }

            usort($earnings, function($a, $b) {
                $orderA = ($a->order_by === "") ? PHP_INT_MAX : $a->order_by;
                $orderB = ($b->order_by === "") ? PHP_INT_MAX : $b->order_by;
                return $orderA - $orderB;
            });
        }
        $data['earnings'] = $earnings;
        $data['deduction'] = $deduction;
        echo json_encode($data);
    }

    public function approve_per_paslip(){
        $selected_schedule = $_POST['selected_schedule'];
        $staff_id = $_POST['staffId'];
        $employment_type = $_POST['employment_type'];
        $this->generate_pdf_payroll_per_payslip($selected_schedule, $staff_id, $employment_type);
        $result = $this->payroll_model->approve_per_paslip($selected_schedule, $staff_id);
        echo json_encode($result);
    }

    public function generate_pdf_payroll_per_payslip($selected_month, $staffId, $employment_type){
        $template = $this->payroll_model->get_email_template_for_payslip($employment_type);
        if($template){
            $data['payslip'] = $this->payroll_model->get_payslip_info($staffId, $selected_month);
            $pdf_html = $this->_create_template_payslip($data['payslip'],$template);
            $update =  $this->payroll_model->update_payroll_html_receipt($pdf_html, $staffId, $selected_month);
            $this->genearte_payslip_pdf($pdf_html, $staffId, $selected_month);
        }
    }

    public function reject_per_payslip(){
        $selected_schedule = $_POST['selected_schedule'];
        $staff_id = $_POST['staffId'];
        $remarks = $_POST['remarks'];
        $result = $this->payroll_model->reject_per_payslip($selected_schedule, $staff_id, $remarks);
        echo json_encode($result);
    }

    public function edit_payroll_data_massgenerate(){
        echo $this->payroll_model->edit_payroll_data_massgenerate();
    }

    public function schedulePayroll_onchagne_approval(){
        $selected_schedule = $_POST['selected_schedule'];
        $stafftypeId = $_POST['stafftypeId'];
        $staff_status = $_POST['staff_status'];
        $login_staff = $_POST['login_staff'];
        $employment_type = $_POST['employment_type'];
        $result = $this->payroll_model->getTemplateData_approval_list($selected_schedule, $stafftypeId, $staff_status, $login_staff, $employment_type);
        foreach ($result as &$res) {
            $total_earnings = floatval($res->total_earnings);
            $total_deductions = floatval($res->total_deduct);
            $net_pay = $total_earnings - $total_deductions;

            $res->net_pay = $net_pay;
        }
        echo json_encode($result);
    }

    public function export_excel_salary_structure(){
        $data['slabs'] = $this->payroll_model->get_payroll_settings();
        $columns = $this->payroll_model->get_payroll_column_table();
        if(!empty($columns)){
            $data['main_content'] = 'management/payroll/export_excelto_salary_structure_new';
        }else{
            $data['main_content'] = 'management/payroll/export_excelto_salary_structure';
        }
        $this->load->view('inc/template', $data);
    }

    private  $payrollColums = array(
        'payroll_master_id',
        'employee_id',
        'staff_id',
        'staff_name',
        'monthly_gross',
        'extra_allowance',
        'hra_fixed',
        'co_ordinator_allowance',
        'ib_retention_allowance',
        'house_master_allowance',
        'academic_grade_pay',
        'vpf',
    );


    public function downloadCsvFormat_salary_structure($value='') {

        $get_staff_data = $this->payroll_model->get_staff_list_for_salary();
        $columns = $this->payroll_model->get_payroll_column_table();
        $exclude_columns = ['slab', 'yearly_gross', 'yearly_ctc','conveyance','special_allowance','medical_allowance','lta','cca','transport_allowance'];
        $csv_columns = [];
        foreach ($columns->payroll_column as $key => $val) {
            if($val->salary_structure == '1' && !in_array($val->column_name, $exclude_columns)){
                if($val->column_name == 'monthly_gross'){
                    $val->column_name = 'monthly_ctc';
                }
                if($val->column_name == 'pf_for_employer'){
                    $val->column_name = 'pf_for_employer_salary';
                }
                array_push($csv_columns, $val->column_name);
            }
        }        
        $staff_details = array('employee_code','staff_id', 'staff_name');
        $csvHeaderarrays[] = array_merge($staff_details, $csv_columns);
        $csvarrays = [];
        foreach ($get_staff_data as $val) {
            $csvarrays[] = array($val->employee_code, $val->smId, $val->name);
        }
        
        $datamerge = array_merge($csvHeaderarrays, $csvarrays);
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="payroll_salary_structure_template.csv"');
        $output = fopen('php://output', 'w');
        foreach ($datamerge as $row) {
            fputcsv($output, $row);
        }
        fclose($output);
        exit();

        // Payroll enabled options
        // $payroll_columns = $this->payroll_model->get_fields_columns_payroll();
        // $enabled_fields = $this->cmodel->getEnabledValues('payroll');
        // $eFields = json_decode($enabled_fields);
        // $enabledOptions = array();
        // foreach ($payroll_columns as $columns) {
        //     $found = 0;
        //     if (!empty($eFields)) {
        //         foreach ($eFields as $eColumn) {
        //             if ($columns ==  $eColumn) {
        //             $found = 1;
        //             break;
        //             }
        //         }
        //     }
        //     if ($found) {
        //         $enabledOptions [] =$columns;
        //     } else {
        //         $disabledOptions [] = $columns;
        //     }
            
        // }

        // End: Payroll enabled options
        // $payrollData = $this->payroll_model->fetchPayrollData($enabledOptions);
        
        // set columns from both payroll_salary and payroll_master table
        // $fields_to_be_added= [];
        // if(!empty($payrollData['salary_fields'])) {
        //     $fields_to_be_added= [];
        //     foreach($payrollData['salary_fields'] as $fkey => $fval) {
        //         if(in_array($fval, $enabledOptions)) {
        //             array_push($fields_to_be_added, $fval);
        //         }
        //     }
        // }
        // array_push($fields_to_be_added, 'slab_id');
        // if(!empty($payrollData['master_fields'])) {
        //     foreach($payrollData['master_fields'] as $fkey => $fval) {
        //         if($fkey != 0 && $fval->Field != 'employee_id' && $fval->Field != 'staff_id') {
        //             array_push($fields_to_be_added, $fval->Field);
        //         }
        //     }
        // }
        // End: set columns from both payroll_salary and payroll_master table

        // if (!empty($payrollData['result']) && is_array($payrollData['result'])) {
        //     $csvArray = array();
        //     $csvArray[] = array(
        //         'payroll_master_id',
        //         'employee_id',
        //         'staff_id',
        //         'staff_name',
        //         'monthly_gross',
        //         'extra_allowance',
        //         'hra_fixed',
        //         'co_ordinator_allowance',
        //         'ib_retention_allowance',
        //         'house_master_allowance',
        //         'academic_grade_pay',
        //         'vpf'
        //     );
        //     foreach($fields_to_be_added as $k => $v) {
        //         array_push($csvArray[0], $v);
        //     }
        //     $csvArray[0]= array_unique($csvArray[0]);
        //     // echo '<pre>'; print_r($csvArray[0]); die();
        //     // $allcolumns= $csvArray[0];
        //     // echo '<pre>'; print_r($csvArray); die();
        //     foreach ($payrollData['result'] as $data) {
        //         $row = array();
        //         foreach ($csvArray[0] as $column) {
        //             $row[] = $data->{$column} ?? ''; 
        //         }
        //         $csvArray[] = $row;
        //     }
        //     header("Content-type: application/csv");
        //     header("Content-Disposition: attachment; filename=\"Salary-Structure_csv".".csv\"");
        //     header("Pragma: no-cache");
        //     header("Expires: 0");

        //     $handle = fopen('php://output', 'w');
        //     foreach ($csvArray as $row) {
        //         fputcsv($handle, $row);
        //     }
        //     fclose($handle);
        //     exit;
        // } else {
        //     echo "No payroll data found."; 
        // }
    }


    public function upload_csvfor_salary_structure(){
        if(isset($_FILES['payroll_data']) && $_FILES['payroll_data']['error'] === UPLOAD_ERR_OK) {
            $file_path = $_FILES['payroll_data']['tmp_name'];
            $salary_arr = [];
            $this->load->library('csvimport'); 
            if ($this->csvimport->get_array($file_path)) {
                $salary_arr = $this->csvimport->get_array($file_path);
            }
            echo json_encode($salary_arr);
        } else {
            echo json_encode(array('error' => 'File upload failed.'));
        }
    }

    public function submit_csv_salary_data(){
        // echo '<pre>abC'; print_r($this->input->post()); die();
        echo  $this->payroll_model->insert_salary_array_Details();
    }

    public function send_approval_staff_id_upload(){
        $payroll_approval_staff_id = $_POST['payroll_approval_staff_id'];
        $_staff_id = $_POST['_staff_id'];
        $schedule_id = $_POST['selected_schedule'];
        echo $this->payroll_model->insert_approval_staff_id($payroll_approval_staff_id, $_staff_id, $schedule_id);
    }

    public function cal_staff_hra(){
        $slabs = $_POST['slabs'];
            $result = $this->payroll_model->cal_staff_hra($slabs);
            echo json_encode($result);
            
    }

    public function approve_mass_paslip(){
        $schedule_id = $_POST['schedule_id']; 
        $staff_id = $_POST['staff_id'];
        $employmentType = $_POST['employmentType'];
        $this->_generate_pdf_payroll_mass($schedule_id, $staff_id, $employmentType);
        $result = $this->payroll_model->approve_masspaslip($schedule_id, $staff_id);
        echo json_encode($result);
    }
    
    private function _generate_pdf_payroll_mass($selected_month, $staffId, $employmentType){
        $template = $this->payroll_model->get_email_template_for_payslip($employmentType);
        if($template){
            $data['payslip'] = $this->payroll_model->get_payslip_info($staffId, $selected_month);
            $pdf_html = $this->_create_template_payslip($data['payslip'],$template);
            $update =  $this->payroll_model->update_payroll_html_receipt($pdf_html, $staffId, $selected_month);
            $this->genearte_payslip_pdf($pdf_html, $staffId, $selected_month);
        }
    }

    public function salary_advance(){
        $data['main_content'] = 'management/payroll/salary_advance';
        $this->load->view('inc/template', $data);
    }

    public function create_salary_advance(){
        $data['staff_list'] = $this->payroll_model->get_staff_list();
        $data['schedules_list'] = $this->payroll_model->get_schedules_list();
        $data['main_content'] = 'management/payroll/create_salary_advance';
        $this->load->view('inc/template', $data);
    }

    public function assign_schedules_advance(){
        $selected_schedule = $_POST['start_schedule']; 
        $result = $this->payroll_model->get_schedules_list();

        $matched_schedules = array();
        foreach ($result as $schedule) {
            if ($selected_schedule <= $schedule->schedule_id) {
                $matched_schedules[] = $schedule;
            }
        }

        echo json_encode($matched_schedules);
    }

    public function advance_submit(){
        echo $this->payroll_model->save_advance_submit();
    } 

    public function get_advance_salary(){
        $result = $this->payroll_model->get_advance_salary();
        echo json_encode($result);
    }

    public function disbursement_bank_ref_number() {
        $disbursement_id = $this->input->post('disbursement_id');
        $schedule_id = $this->input->post('schedule_id');
        $bank_ref_number = $this->input->post('bank_ref_number');
        $result = $this->payroll_model->disbursement_bank_ref_number_update($disbursement_id, $schedule_id, $bank_ref_number);
        echo json_encode($result);
    }

    public function manage_income(){
        $collectPerkTaxMode = $this->settings->getSetting('payroll_collect_perk_tax_mode');
        $data['collectPerkTaxMode'] = $collectPerkTaxMode;
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $currentYear = date('Y');
        $currentMonth = date('n');
        foreach ($data['financial_year'] as $key => $val) {
            if ($currentMonth <= 3) {
                if (date('Y', strtotime($val->from_date)) == ($currentYear - 1)) {
                    $schedule_year = $val->id;
                    break;
                }
            } else {
                if (date('Y', strtotime($val->from_date)) == $currentYear) {
                    $schedule_year = $val->id;
                    break;
                }
            }
        }
        $data['schedule_year'] = $schedule_year;
        //$data['sStatus'] = $this->settings->getSetting('staff_status');
        $data['main_content'] = 'management/payroll/manage_income_tax';
        $this->load->view('inc/template', $data);
    }

    public function reopenInvestmentDeclarationForProof(){
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $currentYear = date('Y');
        $currentMonth = date('n');
        foreach ($data['financial_year'] as $key => $val) {
            if ($currentMonth <= 3) {
                if (date('Y', strtotime($val->from_date)) == ($currentYear - 1)) {
                    $schedule_year = $val->id;
                    break;
                }
            } else {
                if (date('Y', strtotime($val->from_date)) == $currentYear) {
                    $schedule_year = $val->id;
                    break;
                }
            }
        }
        $data['schedule_year'] = $schedule_year;
        $data['main_content'] = 'management/payroll/reopen_investment_declaration_for_proof';
        $this->load->view('inc/template', $data);
    }

    public function investmentDeclarationApproval(){
        $data['staff_details'] = $this->payroll_model->get_staff_details();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $currentYear = date('Y');
        $currentMonth = date('n');
        foreach ($data['financial_year'] as $key => $val) {
            if ($currentMonth <= 3) {
                if (date('Y', strtotime($val->from_date)) == ($currentYear - 1)) {
                    $schedule_year = $val->id;
                    break;
                }
            } else {
                if (date('Y', strtotime($val->from_date)) == $currentYear) {
                    $schedule_year = $val->id;
                    break;
                }
            }
        }
        $data['schedule_year'] = $schedule_year;
        $data['main_content'] = 'management/payroll/investment_declaration_approval';
        $this->load->view('inc/template', $data);
    }

    public function get_income_declaration_details(){
        $schedule_year = $_POST['schedule_year'];
        $staff_status = $_POST['staff_status_income'];
        $result = $this->payroll_model->get_income_declaration_details($schedule_year, $staff_status);
        echo json_encode($result);
    }

    public function get_staff_rent_details(){
        $schedule_year = $_POST['schedule_year'];
        $staff_id = $_POST['staff_id'];
        $result = $this->payroll_model->get_staff_rent_details($schedule_year, $staff_id);
        echo json_encode($result);
    }

    public function get_payroll_declaration_status() {
        $schedule_year_id = $_POST['schedule_year_id'];
        $result = $this->payroll_model->get_payroll_declaration_status($schedule_year_id);
        echo json_encode($result);
    }

    public function get_income_open_data(){
        $schedule_year = $_POST['schedule_year'];
        $result = $this->payroll_model->get_income_open_data($schedule_year);
        echo json_encode($result);
    }


    public function incometax_declaration_unlock(){
        $schedule_year = $_POST['schedule_year'];
        $result = $this->payroll_model->incometax_declaration_unlock($schedule_year);
        echo json_encode($result);
    }

    public function income_tax_staff_approve(){
        $staff_id = $_POST['staff_id'];
        $financial_year = $_POST['financial_year'];
        $result = $this->payroll_model->income_tax_staff_approve($staff_id, $financial_year);
        echo json_encode($result);
    }

    public function income_tax_staff_reopen(){
        $staff_id = $_POST['staff_id'];
        $financial_year = $_POST['financial_year'];
        $result = $this->payroll_model->income_tax_staff_reopen($staff_id, $financial_year);
        echo json_encode($result);
    }

    public function income_tax_declaration_close(){
        $schedule_year = $_POST['schedule_year'];
        $result = $this->payroll_model->income_tax_declaration_close($schedule_year);
        echo json_encode($result);
    }

    public function manage_increments(){
        $data['financial_years']=$this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $filtered_years = [];
        foreach ($data['financial_years'] as $fy) {
            if (!empty($fy->from_date)) {
                $year = date('Y', strtotime($fy->from_date));
                if ($year >= $current_year) {
                    $filtered_years[] = $fy;
                }
            }
        }
        $data['financial_years'] = $filtered_years;
        $data['title'] = 'Manage Increments';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/manage_increments';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_increment_cycle_by_id(){
        $result = $this->payroll_model->get_increment_cycle_by_id($_POST['cycleId']);
        echo json_encode($result);
    }

    public function add_increment_cycle(){
        echo json_encode($this->payroll_model->add_increment_cycle($_POST));
    }

    public function update_increment_cycle(){
        echo json_encode($this->payroll_model->update_increment_cycle($_POST));
    }

    public function get_increment_cycle(){
        $result = $this->payroll_model->get_increment_cycle();
        echo json_encode($result);
    }

    public function get_payroll_increment_approval_cycle_type(){
        $incrementFrequency = $_POST['incrementFrequency'];
        $allowed = ['monthly', 'yearly'];
        if(!in_array($incrementFrequency, $allowed)){
            echo json_encode(array('error'=>'Please Set The Increment Frequency.'));
            return;
        }
        if(trim($incrementFrequency) == ''){
            echo json_encode(array('error'=>'Please Set The Increment Frequency.'));
            return;
        }
        $staff_id = isset($_POST['staff_id']) ? $_POST['staff_id'] : 0;
        $result = $this->payroll_model->get_payroll_increment_data_matching_with_salary_structure($_POST['id']);
        $monthly_gross = 0;
        $slabSettings = [];
        if(!empty($result->salary_data)){
            if (trim($incrementFrequency) == 'monthly') {
                $monthly_gross = $result->salary_data->monthly_gross + $result->total_amount;
                // $result->salary_data->yearly_ctc = $monthly_gross * 12;
            } else {
                $yearCTC = $result->salary_data->yearly_ctc + $result->total_amount;
                $monthly_gross = $yearCTC / 12;
            }
        }
        if(!empty($result->slab_setting)){
            unset($result->salary_data->lta);
            unset($result->salary_data->pf_for_employer);
            unset($result->salary_data->pf);
            unset($result->salary_data->cca);
            $slabSettings = (object) array_merge((array) $result->slab_setting, (array) $result->salary_data);
        }
        $age_pt_cal= $result->staff_details->age_above_60;
        if (is_object($slabSettings)) {
            $slabSettings->staff_id = $staff_id;
        } else {
            $slabSettings['staff_id'] = $staff_id;
        }
        $calResult = $this->_calculate_payroll_salary($monthly_gross, $slabSettings, $age_pt_cal);
        echo json_encode(array('increment_salary'=>$calResult, 'total_mount' => $result->total_amount, 'effective_from' => $result->schedule_name, 'previous_salary_data'=>$result->salary_data,'staff_details'=>$result->staff_details,'staff_increment_cycle_id'=>$result->staff_increment_cycle_id));
    }

    public function get_schedule_years(){
        $financial_year_id=$_POST['financial_year'];
        $dataFor = '';
        if(isset($_POST['dataFor'])){
            $dataFor = $_POST['dataFor'];
        }
        $scheduleId = NULL;
        if(isset($_POST['edit_inc_schedule_id'])){
            $scheduleId = $_POST['edit_inc_schedule_id'];
        }
        $result = $this->payroll_model->get_financial_yearwise_data($financial_year_id, $dataFor, $scheduleId);
        echo json_encode($result);
    }

    public function add_increments_schedule_year($cycle_id){
        $data['cycle_id'] = $cycle_id;
        $data['increment_cycle'] = $this->payroll_model->get_increment_cyle_details($cycle_id);
        $allowed = ['monthly', 'yearly'];
        if(!in_array($data['increment_cycle']->increment_frequency, $allowed)){
            $data['increment_cycle']->increment_frequency = '';
        }
        if(trim($data['increment_cycle']->increment_frequency) == 'Monthly')
            $data['increment_cycle']->increment_frequency = 'monthly';
        else if(trim($data['increment_cycle']->increment_frequency) == 'Yearly')
            $data['increment_cycle']->increment_frequency = 'yearly';
        $data['incrementFrequency'] = $data['increment_cycle']->increment_frequency;
        $data['displayActions'] = $data['increment_cycle']->increment_frequency == '' ? 'display: none; ' : '';
        $data['increment_types']= $this->payroll_model->get_increment_types();
        $data['staff']= $this->payroll_model->get_staff_list_increment($cycle_id);
        $data['disableAdd'] = '';
        $data['disableEdit'] = '';
        $today = date('d-m-Y');
        if(strtotime($data['increment_cycle']->cycle_end_date) < strtotime($today)){
            $data['disableAdd'] = 'display: none; ';
            $data['disableEdit'] = 'disabled';
        }
        if($this->authorization->isSuperAdmin() || $this->authorization->isAuthorized('PAYROLL.ADD_INCREMENTS_PREVIOUS_MONTH')){
            $data['disableAdd'] = '';
            $data['disableEdit'] = '';
            $data['displayActions'] = '';
        }
        $data['title'] = $data['increment_cycle']->cycle_name . ' - ' . $data['increment_cycle']->schedule_name;
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/add_increments_schedule_year_amount';
        }
        $this->load->view('inc/template', $data);
    }

    public function validateStaffList(){
        $result = $this->payroll_model->get_staff_list_increment($_POST['cycle_id']);
        echo json_encode($result);
    }

    public function add_increment(){
        return $this->payroll_model->add_increment($_POST);
    }

    public function add_increment_amount(){
        echo json_encode($this->payroll_model->add_increment_amount($_POST));
    }

    public function get_payroll_increments(){
        $result = $this->payroll_model->get_payroll_increments($_POST['schedule_id']);
        echo json_encode($result);
    }

    public function get_payroll_increments_amount(){
        $incrementFrequency = $_POST['incrementFrequency'];
        $allowed = ['monthly', 'yearly'];
        if(!in_array($incrementFrequency, $allowed)){
            echo json_encode(array('error'=>'Please Set The Increment Frequency.'));
            return;
        }
        $result = $this->payroll_model->get_payroll_increments_amount($_POST['cycle_id'], $_POST['incrementFrequency']);
        echo json_encode($result);
    }

    public function increment_types(){
        $data['title'] = 'Manage Increment Types';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/increment_types_view';
        }
        $this->load->view('inc/template', $data);
    }

    public function add_increment_type(){
        return $this->payroll_model->add_increment_type($_POST);
    }

    public function get_increment_type(){
        $result= $this->payroll_model->get_increment_type();
        echo json_encode($result);
    }

    public function change_status_increment_cycle(){
        echo json_encode($this->payroll_model->change_status_increment_cycle($_POST['status'],$_POST['id']));
    }

    public function deleteIncrementType(){
        echo json_encode($this->payroll_model->deleteIncrementType($_POST['id']));
    }

    public function update_status_applied(){
        return $this->payroll_model->update_status_applied($_POST['schedule_id']);
    }

    public function manage_increments_approvals(){
        $data['title'] = 'Manage Increments Approvals';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/manage_increments_approval';
        }
        $this->load->view('inc/template', $data);
    }

    public function add_increments_approvals_schedule_year($cycle_id){
        $data['cycle_id']= $cycle_id;
        $data['increment_cycle'] = $this->payroll_model->get_increment_cyle_details($cycle_id);
        $data['disableEdit'] = '';
        $today = date('d-m-Y');
        if(strtotime($data['increment_cycle']->cycle_end_date) < strtotime($today)){
            $data['disableEdit'] = 'disabled';
        }
        if($this->authorization->isAuthorized('PAYROLL.ADD_INCREMENTS_PREVIOUS_MONTH')){
            $data['disableEdit'] = '';
        }
        // $data['schedule_month']= $this->payroll_model->get_schedule_month($schedule_id);
        // echo "<pre>"; print_r($data['increment_cycle']); die();
        $data['title'] = $data['increment_cycle']->cycle_name . ' - ' . $data['increment_cycle']->schedule_name;
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/add_increments_approvals_schedule_year';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_payroll_increment_approval(){
        $incrementFrequency = $_POST['incrementFrequency'];
        $allowed = ['monthly', 'yearly'];
        if(!in_array($incrementFrequency, $allowed)){
            echo json_encode(array('error'=>'Please Set The Increment Frequency.'));
            return;
        }
        $result = $this->payroll_model->get_payroll_increment_approval($_POST['cycle_id'], $_POST['incrementFrequency']);
        echo json_encode($result);
    }

    public function update_status_approved(){
        $staff_increment_cycle_id = $_POST['staff_increment_cycle_id'];
        $increment_salary = json_decode($_POST['increment_salary']);
        $previous_salary_data = json_decode($_POST['previous_salary_data']);
        echo json_encode($this->payroll_model->update_status_approved($staff_increment_cycle_id, $increment_salary, $previous_salary_data));
    }

    public function update_status_rejected(){
        echo $this->payroll_model->update_status_rejected($_POST['staff_increment_cycle_id'],$_POST['text'], $_POST['staff_id']);
    }

    public function remove_increment(){
        $input = $this->input->post();
        $staff_increment_cycle_id = $input['staff_increment_cycle_id'];
        $increment_salary = json_decode($input['increment_salary']);
        $previous_salary_data = json_decode($input['previous_salary_data']);
        echo json_encode($this->payroll_model->remove_increment($staff_increment_cycle_id, $increment_salary, $previous_salary_data));
    }

    public function get_payroll_increments_individual(){
        $result = $this->payroll_model->get_payroll_increments_individual($_POST['id']);
        echo json_encode($result);
    }

    public function removeIncrementInProvideIncrement(){
        echo json_encode($this->payroll_model->removeIncrementInProvideIncrement($_POST['id']));
    }

    public function get_payroll_increments_individual_amount(){
        $incrementFrequency = $_POST['incrementFrequency'];
        $allowed = ['monthly', 'yearly'];
        if(!in_array($incrementFrequency, $allowed)){
            echo json_encode(array('error'=>'Please Set The Increment Frequency.'));
            return;
        }
        $result = $this->payroll_model->get_payroll_increments_individual_amount($_POST['id'], $_POST['incrementFrequency']);
        echo json_encode($result);
    }

    public function update_increment(){
        echo $this->payroll_model->update_increment($_POST);
    }

    public function update_increment_amount(){
        echo json_encode($this->payroll_model->update_increment_amount($_POST));
    }

    public function get_payroll_increments_individual_edit_data(){
        $result = $this->payroll_model->get_payroll_increments_individual_edit_data($_POST['id']);
        echo json_encode($result);
    }

    public function get_payroll_increments_individual_edit_data_amount(){
        $result = $this->payroll_model->get_payroll_increments_individual_edit_data_amount($_POST['id']);
        echo json_encode($result);
    }

    public function upload_csvfor_increment(){
        if(isset($_FILES['payroll_data']) && $_FILES['payroll_data']['error'] === UPLOAD_ERR_OK) {
            $file_path = $_FILES['payroll_data']['tmp_name'];
            $salary_arr = [];
            $this->load->library('csvimport'); 
            if ($this->csvimport->get_array($file_path)) {
                $salary_arr = $this->csvimport->get_array($file_path);
            }
            echo json_encode($salary_arr);
        } else {
            echo json_encode(array('error' => 'File upload failed.'));
        }
    }

    public function downloadCsvFormat_staff_increment(){

        $staff_details = $this->payroll_model->get_staff_details_for_payslip_salary();
        // $staff = array(
        //     'staff_id'=>1,
        //     'staff_name'=>"Mohan"
        // );

        $CycleDetails = $this->payroll_model->get_cycle_type_all_details();

        // $cycle = array(
        //     'cycle_name'=>'type1',
        //     'cycle_id'=>1,
        // );
        $megeValue = array_merge($staff_details, $CycleDetails);

        $Keyname = [];
        foreach ($CycleDetails as $key => $val) {
            array_push($key, $Keyname);
        }
        
        $csvHeader = ['staff_id','cycle_id','staff_name'];

        $mergeheader[] = array_merge($csvHeader, $Keyname);
        echo "<pre>"; print_r($mergeheader); die();

        $csvArry = [];
        foreach ($megeValue as $key => $val) {
            $csvArry[] = array($val->staff_id, $val->cycle_id, $val->staff_name);
        }

        $csvArray = array_merge ($mergeheader, $csvArry);

        // $data= $this->payroll_model->downloadCsvFormat_staff_increment();


        // if (!empty($payrollData['result']) && is_array($payrollData['result'])) {
        //     $csvArray = array();
        //     $csvArray[] = array(
        //                         'payroll_master_id',
        //                         'employee_id',
        //                         'staff_id',
        //                         'staff_name',
        //                         'monthly_gross',
        //                         'extra_allowance',
        //                         'hra_fixed',
        //                         'co_ordinator_allowance',
        //                         'ib_retention_allowance',
        //                         'house_master_allowance',
        //                         'academic_grade_pay',
        //                         'vpf'
        //                         );
        //     foreach($fields_to_be_added as $k => $v) {
        //         array_push($csvArray[0], $v);
        //     }
        //     $csvArray[0]= array_unique($csvArray[0]);
        //     // echo '<pre>'; print_r($csvArray[0]); die();
        //     // $allcolumns= $csvArray[0];
        //     // echo '<pre>'; print_r($csvArray); die();
        //     foreach ($payrollData['result'] as $data) {
        //         $row = array();
        //         foreach ($csvArray[0] as $column) {
        //             $row[] = $data->{$column} ?? ''; 
        //         }
        //         $csvArray[] = $row;
        //     }

            header("Content-type: application/csv");
            header("Content-Disposition: attachment; filename=\"Salary-Increment-Structure_csv".".csv\"");
            header("Pragma: no-cache");
            header("Expires: 0");

            $handle = fopen('php://output', 'w');
            foreach ($csvArray as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
            exit;
        // } else {
        //     echo "No payroll data found."; 
        // }
    }

    public function downloadCsvFormat_incrementsData($value='') {
        $template_name = $this->input->get('template_name') ?: 'Increment Template';
        $current_datetime = date('d-M-Y_H:i:s');
        $template_name .= '_' . $current_datetime;
        $increments_types= $this->payroll_model->get_increments_types();
        if(empty($increments_types)) {
                echo "Increment Types Not Found."; 
                exit;
        }
        
        $incData = $this->payroll_model->fetch_increments_data($increments_types);
        
        if (!empty($incData) && is_array($incData)) {
            $csvArray = array();
            $csvArray[] = array(
                                'staff_id',
                                'employee_code',
                                'staff_name',
                                'department',
                                'designation'
                            );
            foreach($increments_types as $ik => $iv) {
                array_push($csvArray[0], $iv);
            }

            foreach ($incData as $data) {
                $row = array();
                foreach ($csvArray[0] as $column) {
                    $row[] = $data->{$column} ?? ''; 
                }
                $csvArray[] = $row;
            }

            header("Content-type: application/csv");
            $yearId = $this->acad_year->getAcadYearId();
            header("Content-Disposition: attachment; filename=\"$template_name".".csv\"");
            header("Pragma: no-cache");
            header("Expires: 0");

            $handle = fopen('php://output', 'w');
            foreach ($csvArray as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
            exit;
        } else {
            echo "No increment data found."; 
        }
    }

    public function upload_csvfor_increment_structure(){
        if(isset($_FILES['increment_data']) && $_FILES['increment_data']['error'] === UPLOAD_ERR_OK) {
            $file_path = $_FILES['increment_data']['tmp_name'];
            $salary_arr = [];
            $this->load->library('csvimport'); 
            if ($this->csvimport->get_array($file_path)) {
                $salary_arr = $this->csvimport->get_array($file_path);
            }
            echo json_encode($salary_arr);
        } else {
            echo json_encode(array('error' => 'File upload failed.'));
        }
    }

    public function submit_mass_increments_of_a_staff() {
        echo $this->payroll_model->submit_mass_increments_of_a_staff();
    }

    public function get_slab_settings_new(){
        $slabs = $_POST['slabs'];
        $monthly_gross = $_POST['monthly_gross'];
        $basic_salary = isset($_POST['basic_salary']) ? $_POST['basic_salary'] : 0;
        $staff_id = $_POST['staff_id'];
        $result = $this->payroll_model->get_slab_settings_details($slabs);

        $salary_data = $this->payroll_model->get_salary_edit_staff_data($staff_id);
        if(!empty($salary_data)){
            unset($salary_data->lta);
            unset($salary_data->pf_for_employer);
            unset($salary_data->pf);
            unset($salary_data->cca);
            $mergeSalary = $this->merge_salary_edit_payslip_data($result, $salary_data);
            $result = (object) $mergeSalary;
        }
        $result->staff_id = $staff_id;
        $staff_details = $this->payroll_model->getStaffname($staff_id);
        $age_pt_cal= $staff_details->age_above_60;
        $calResult = $this->_calculate_payroll_salary($monthly_gross, $result, $age_pt_cal, $basic_salary);
        echo json_encode($calResult);
    }

    private function merge_salary_edit_payslip_data($slab_data,$salary_data){
        $slab_data = (array) $slab_data;
        $salary_data = (array) $salary_data;

        // Old
        // foreach ($slab_data as $key => $value) {
        //     if ((empty($value) || $value === '' || $value == '0.00' || $value == '0') && isset($salary_data[$key])) {
        //         $slab_data[$key] = $salary_data[$key];
        //     }
        // }

        // New
        $includeCustomValues = ['custom1', 'custom2', 'custom3', 'custom4', 'custom5', 'custom6', 'custom7', 'custom8', 'custom9', 'custom10'];
        foreach ($salary_data as $key => $value) {
            if (isset($slab_data[$key])) {
                $slab_data[$key] = $value;
            } else if(in_array($key, $includeCustomValues)) {
                $slab_data[$key] = $value;
            }
        }
        return  $slab_data; 
    }

    private function _calculate_payroll_salary($monthly_gross, $result, $age_pt_cal, $basic_salary = 0) {
        $monthly_basic_salary = 0;
        $academicgradepay = ($result->academic_grade_pay != '') ? $result->academic_grade_pay : 0;
        $hra = 0;
        $da = 0;
        $ma_slab = 0;
        $cca_slab = 0;
        $staffta = 0;
        $pf = 0;
        $pf_for_employer = 0;
        $ltaCal = 0;
        $ltaRules = 0;
        $sa = 0;
        // Calculate monthly basic salary based on the basic mode
        if ($result->basic_mode == 2) {
            $monthly_basic_salary = round(($monthly_gross * $result->basic_salary) / 100, 2);
        }else{
            if ($basic_salary > 0 && abs((float)$basic_salary - $result->basic_salary) > 1) {
                // UI passed an updated value; use it directly
                $monthly_basic_salary = round($basic_salary, 2);
            } else {
                // Initial case: add both
                $monthly_basic_salary = round($result->basic_salary, 2);
            }
        }

        // Calculate HRA
        if ($result->hra_algo == 'percentage') {
            $hra = round((($monthly_basic_salary + $academicgradepay) * $result->hra) / 100, 2);
        } else {
            $hra = round($result->hra + $academicgradepay, 2);
        }
    
        // Calculate yearly gross
        $yearlyGross = round($monthly_basic_salary * 12, 2);
    
        // Calculate DA
        if ($result->da_algo == 'percentage') {
            $da = round((($monthly_basic_salary + $academicgradepay) * $result->da) / 100, 2);
        } else {
            $da = round($result->da + $academicgradepay, 2);
        }
        
        // Calculate Medical Allowance slab
        if ($result->medical_allowance_rules == '1') {
            $ma_slab = $result->medical_allowance;
        } elseif ($result->medical_allowance_rules == '2' && $monthly_basic_salary > 20000) {
            $ma_slab = $result->medical_allowance;
        } elseif ($result->medical_allowance_rules == '3' && $monthly_gross >= 20000) {
            $ma_slab = $result->medical_allowance;
        }
        // Calculate CCA slab
        if ($result->cca_algo === 'percentage') {
            $cca_slab = round(($monthly_basic_salary * $result->cca) / 100, 2);
        } else {
            $cca_slab = $result->cca;
        }
    
        // Calculate staff TA
        // $staffta = round($monthly_gross - $monthly_basic_salary - $hra - $da - $ma_slab - $cca_slab - $result->transport_allowance, 2);
        // Calculate Professional Tax (PT)
        $state =  json_decode($this->settings->getSetting('State Wise PT'));
        // echo '<pre>';print_r($payrollData);die();
        // echo "<pre>";print_r($state);
        // echo "<pre>";print_r($monthly_gross);
        // if ($monthly_gross >= 25000 && $age_pt_cal == 1) {
        //     $pt = 200;
        // } else {
        //     $pt = 0;
        // }
        // if(!empty($state)){
        //     $pt = $this->_calculate_state_wise_pt($state,$age_pt_cal, $monthly_gross, $result->staff_id);
        // }
        // echo "<pre>";print_r($pt);die();

        $da_basicSalary = $monthly_basic_salary + $da;

        // Rules
        // Rule 1 - If Basic + DA > ₹15,000: ₹1,800, otherwise 12% of Basic salary.
        // Rule 2 - If Basic + DA > ₹15,000: 12%, otherwise 12% of Basic salary.
        // Rule 3 - No contribution (₹0).
        // Rule 4 - If Basic + DA > ₹25,000: ₹1,800, otherwise 10% of Basic salary.
        // Rule 5 - Salary PF calculated for present days. Works only in Payslip Generation, Amount must be entered in salary.
        // Rule 6 - 12% of ₹15,000 if Basic + DA ≥ ₹15,000, otherwise 12% of Basic + DA.
        // Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days. Works only in Payslip Generation, Amount must be entered in salary.

        // Calculate PF
        if ($result->pf == '1.00') {
            $pf = ($da_basicSalary >= 15000) ? 1800 : round(($da_basicSalary * 12) / 100, 2);
        } elseif ($result->pf == '2.00') {
            $pf = round(($da_basicSalary * 12) / 100, 2);
        } elseif ($result->pf == '4.00') {
            $pf = ($da_basicSalary >= 25000) ? 1800 : round(($da_basicSalary * 10) / 100, 2);
        } elseif ($result->pf == '3.00') {
            $pf = 0;
        } elseif ($result->pf == '6.00'){
            $pf = ($da_basicSalary >= 15000) ? round((15000 * 12) / 100, 2) : round(($da_basicSalary * 12) / 100, 2);
        }

        // Calculate PF for Employer
        if ($result->pf_for_employer == '1.00') {
            $pf_for_employer = ($da_basicSalary >= 15000) ? 1800 : round(($da_basicSalary * 12) / 100, 2);
        } elseif ($result->pf_for_employer == '2.00') {
            $pf_for_employer = round(($da_basicSalary * 12) / 100, 2);
        } elseif ($result->pf_for_employer == '4.00') {
            $pf_for_employer = ($da_basicSalary >= 25000) ? 1800 : round(($da_basicSalary * 10) / 100, 2);
        } elseif ($result->pf_for_employer == '3.00') {
            $pf_for_employer = 0;
        } elseif ($result->pf_for_employer == '6.00'){
            $pf_for_employer = ($da_basicSalary >= 15000) ? round((15000 * 13) / 100, 2) : round(($da_basicSalary * 13) / 100, 2);
        }

        // Calculate LTA rules
        if ($result->lta_slab == 'percentage') {
            if($result->lta_rules == '1' || $result->lta_rules == '2'){
                $ltaCal = round(($monthly_basic_salary * $result->lta) / 100, 2);
            }

            if($result->lta_rules == '3'){
                $ltaCal = round(($monthly_gross * $result->lta) / 100, 2);
            }
        } else {
            $ltaCal = $result->lta;
        }

        if ($result->lta_rules == '1') {
            $ltaRules = $ltaCal;
        } elseif ($result->lta_rules == '2' && $monthly_gross > 35000) {
            $ltaRules = $ltaCal;
        } elseif ($result->lta_rules == '3' && $monthly_gross >= 27500) {
            $ltaRules = $ltaCal;
        } elseif ($result->lta_rules == '5') {
            if ($result->lta_slab == 'percentage') {
                if($da_basicSalary >= 27500){
                    $ltaRules = round(($da_basicSalary * $result->lta) / 100, 2);
                }else{
                    $ltaRules = 0;
                }
            } else {
                $ltaRules = $result->lta;
            }
        }
        $get_columnspf = $this->payroll_model->get_payroll_column_table();

        $sa_pf_employer = 0;
        $ta = 0;
        $sa_custom1 = 0;
        foreach ($get_columnspf->payroll_column as $key => $val) {
            // if($val->column_name == 'pf_employee_contribution' && $val->calculate_number_of_days_present == 1){
            //     $pf = ($pf == '1800') ? round(($pf /  $no_of_days * $numberofPresentDays), 2) : $pf;
            // }
            // if($val->column_name == 'pf_for_employer' && $val->calculate_number_of_days_present == 1){
            //     $pf_for_employer = ($pf_for_employer == '1800') ? round(($pf_for_employer /  $no_of_days * $numberofPresentDays), 2) : $pf_for_employer;
            // }
            if ($val->include_ctc == 1 &&  $val->column_name == 'pf_for_employer') {
                $sa_pf_employer =  $pf_for_employer;
            }
            if ($val->include_ctc == 1 &&  $val->column_name == 'transport_allowance') {
                $ta =  $result->transport_allowance;
            }
            if($val->include_ctc == 1 &&  $val->column_name == 'custom1'){
                $sa_custom1 = isset($result->custom1) ? $result->custom1 : 0 ;
            }
        }

        // Calculate Special Allowance (SA)
        $sa = round($monthly_gross - $monthly_basic_salary - $hra - $da - $result->conveyance - $ma_slab - $ltaRules - $cca_slab - $staffta - $sa_pf_employer - $ta - $sa_custom1, 2);
        $esi = 0;
        if ($result->esi == 1) {
            $esi = 0;
        }else if ($result->esi == '2.00'){
            $esi = round($monthly_gross*0.75/100, 2);
        }else if($result->esi == '3.00'){
            $esi = round($monthly_gross*1.75/100,2);
        }else if($result->esi == '4.00'){
            if (round($monthly_gross) < '21000.00') {
                $esi = round($monthly_gross*0.75/100,2);
            }else{
                $esi = 0;
            }
        }

        $yearlyCTC = $monthly_gross * 12;
        $columns = $this->payroll_model->get_payroll_column_table();
        $earnings = [];
        $deduction = [];
        foreach ($columns->payroll_column as $key => $val) {
            if($val->type == 'earnings'){
                array_push($earnings, $val);
            }
            if($val->type == 'deduction'){
                array_push($deduction, $val);
            }
        }

        // Return calculated values
        $result =  array(
            'monthly_gross' => $monthly_gross,
            'monthly_basic_salary' => $monthly_basic_salary,
            'staff_hra' => $hra,
            'yearly_gross' => $yearlyGross,
            'yearly_ctc' => $yearlyCTC,
            'staff_da' => $da,
            'medical_allowance' => $ma_slab,
            'cca' => $cca_slab,
            'staffta' => $staffta,
            'professional_tax' => 0,
            'pf_employee_contribution' => $pf,
            'pf_for_employer' => $pf_for_employer,
            'lta' => $ltaRules,
            'special_allowance' => $sa,
            'conveyance'=>$result->conveyance,
            'transport_allowance'=>$result->transport_allowance,
            'extra_allowance'=>$result->extra_allowance,
            'hra_fixed'=>$result->hra_fixed,
            'co_ordinator_allowance'=>$result->co_ordinator_allowance,
            'ib_retention_allowance'=>$result->ib_retention_allowance,
            'house_master_allowance'=>$result->house_master_allowance,
            'vpf'=>$result->vpf,
            'esi'=>$esi,
            'mode_monthly_basic_salary'=>$result->basic_mode,
            'staff_id' => $result->staff_id,
            // 'transport' => $result->transport,
            'custom1'=> isset($result->custom1) ? $result->custom1 : 0,
        );
        $result['earnings'] = 0;
        $result['deducation'] = 0;
        foreach ($earnings as $obj) {
            if ($obj->total_earings_include == 1) {
                $key = $obj->column_name;
                if (isset($result[$key])) {
                    $result['earnings'] += floatval($result[$key]);
                }
            }
        }

        $result['professional_tax'] = $this->_calculate_state_wise_pt($state,$age_pt_cal, $result['earnings'], $result['staff_id']);

        foreach ($deduction as $obj) {
            if ($obj->total_deduction_include == 1) {
                $key = $obj->column_name;
                if (isset($result[$key])) {
                    $result['deducation'] += floatval($result[$key]);
                }
            }
        }

        return $result;
    }

    public function downloand_csv_for_monthly_payslip() {
        $staff_ids = $_POST['staff_ids'];
        $schedule_month = $_POST['schedule_month'];
        $get_schedule_month = $this->payroll_model->get_schedule_month_csv($schedule_month);
        $get_staff_data = $this->payroll_model->get_all_staff_data_monthly_payroll_csv($staff_ids);
        $columns = $this->payroll_model->get_payroll_column_table();
        $csv_columns = [];
        $exclude_columns = ['tds']; 
        foreach ($columns->payroll_column as $key => $val) {
            if ($val->upload_csv == '1') {
                if ($get_schedule_month->is_automatic_tds_calculation_enabled == 'Yes' && in_array($val->column_name, $exclude_columns)) {
                    continue;
                }
                array_push($csv_columns, $val->display_name);
            }
        }

        $staff_details = array('employee_id', 'staff_id','schedule_id','schedule_month','staff_name','lop');

        $csvHeaderarrays[] = array_merge($staff_details, $csv_columns);

        $csvarrays = [];
        foreach ($get_staff_data as $val) {
            $leave_info_new = $this->payroll_model->getStaffLeaveInfo($val->staff_id, $schedule_month);
            $leaveCount = 0;
            if(!empty($leave_info_new)){
                foreach ($leave_info_new as $key => $value) {
                    $leaveCount += $value->leave_count;
                }
            }
            $csvarrays[] = array($val->employee_id, $val->staff_id, $schedule_month, $get_schedule_month->schedule_name, $val->staff_name, $leaveCount);
        }
        
        $datamerge = array_merge($csvHeaderarrays, $csvarrays);
        $selected_schedule = $this->payroll_model->edit_schedules_rowid($schedule_month);
        $selected_schedule_name= $selected_schedule->schedule_name;
        $current_datetime = date('d-M-Y_H-i-s');
        $template_name = 'Payslip_data_' . $selected_schedule_name . '_' . $current_datetime;
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="'.$template_name.'.csv"');
        $output = fopen('php://output', 'w');
        foreach ($datamerge as $row) {
            fputcsv($output, $row);
        }
        fclose($output);
        exit();
    }

    // public function upload_csvfor_monthly_data(){
    //     $this->load->library('csvimport'); 
    //     $creDir = 'uploads/payroll';
    //     if (!is_dir($creDir)) {
    //       mkdir($creDir, 0777, TRUE);
    //     }

    //     if ($_FILES['payroll_data']['name'] != "") {
    //       $ret_val = $this->upload('payroll_data', $creDir);
    //       if ($ret_val['status'] == 'success') {
    //         $file_data = $ret_val['data'];
    //       } else {
    //         $file_data = $ret_val['data'];
    //       }
    //     }else{
    //        $file_data="";
    //     }
    //     $file_path = 'uploads/payroll/'.$file_data['file_name'];
    //     if ($this->csvimport->get_array($file_path)) {
    //         $data_arr = $this->csvimport->get_array($file_path);
    //         echo "<pre>"; print_r($data_arr); die();
    //         foreach ($data_arr as $key => $std) {
    //             $data[] = array( 
    //                 'staff_id' => $std['staff_id'], 
    //                 'schedule_id' => $std['schedule_id'],
    //                 'tds' => $std['tds'],
    //                 'reimbursement' => $std['reimbursement'],
    //             );

    //         }
    //         $result = $this->db->insert_batch('new_payroll_tds_reimbursement_data',$data);
    //         if($result){ 
    //             $this->session->set_flashdata('flashSuccess', 'Data Upload Successful');
    //           }
    //           else{
    //             $this->session->set_flashdata('flashError', 'Something Went Wrong');
    //         }
    //         redirect('management/payroll/import_excelto_erp');
    //     }
    // }

    public function import_monthly_payroll_additional_data(){
        $staff_payroll = $_POST['staff_payroll'];
        echo $this->payroll_model->insert_import_payroll_data($staff_payroll);
    }

    public function get_payroll_structure_data_for_staff(){
        $selected_schedule = $_POST['selected_schedule'];
        $stafftypeId = $_POST['stafftypeId'];
        $staff_status = $_POST['staff_status'];
        $employment_type = $_POST['employment_type'];

        $salaryStrucutre = $this->payroll_model->payroll_structure_staff_data($selected_schedule, $stafftypeId, $staff_status, $employment_type);
        $salaryDataArray = [];
        $salaryData = [];
        if(!empty($salaryStrucutre)){
            foreach ($salaryStrucutre as $key => $val) {
                if(!empty($val->salary_data)){
                    array_push($salaryDataArray, $val);
                }
                if($employment_type == 'Consultant'){
                    array_push($salaryDataArray, $val);
                }
            }
            if(!empty($salaryDataArray)){
                $salaryData =  $this->_construct_staff_payslip_data($salaryDataArray);
            }   
        }
        echo json_encode($salaryData);
    }

    private function handlePayslipData($data, &$PayslipStatus, &$disabled, &$edit_disabled, &$ApprovalDisabled, &$approval_name, &$no_of_present, &$lop) {
        $PayslipStatus = 'Generated';
        $disabled = '';
        $edit_disabled = '';
        $ApprovalDisabled = '';
        $approval_name = '';

        if (!empty($data->payslip_data->approval_staff_id)) {
            $this->handleApprovalPending($data, $PayslipStatus, $disabled, $edit_disabled, $ApprovalDisabled, $approval_name);
        }

        switch ($data->payslip_data->approval_status) {
            case 0:
                $this->handleApproved($PayslipStatus, $disabled, $edit_disabled, $ApprovalDisabled, $approval_name);
                break;
            case 2:
                $this->handleRejected($PayslipStatus, $disabled, $ApprovalDisabled, $edit_disabled, $approval_name);
                break;
        }

        $no_of_present = $data->payslip_data->no_of_days_present;
        $lop = 0;
    }

    private function handleApprovalPending($data, &$PayslipStatus, &$disabled, &$edit_disabled, &$ApprovalDisabled, &$approval_name) {
        $PayslipStatus = 'Approval Pending';
        $disabled = 'disabled';
        $edit_disabled = 'pointer-events: none';
        $ApprovalDisabled = '';

        $get_approval_staff = $this->payroll_model->getStaffName_id($data->payslip_data->approval_staff_id);
        $approval_name = '<br>(' . $get_approval_staff . ')';
    }

    private function handleApproved(&$PayslipStatus, &$disabled, &$edit_disabled, &$ApprovalDisabled, &$approval_name) {
        $PayslipStatus = 'Approved';
        $disabled = 'disabled';
        $ApprovalDisabled = 'disabled';
        $edit_disabled = 'pointer-events: none';
        $approval_name = '';
    }

    private function handleRejected(&$PayslipStatus, &$disabled, &$ApprovalDisabled, &$edit_disabled, &$approval_name) {
        $PayslipStatus = 'Rejected';
        $disabled = '';
        $ApprovalDisabled = 'disabled';
        $edit_disabled = '';
        $approval_name = '';
    }


    private function handleAutomaticTDSCalculation($data, &$PayslipStatus, &$disabled, &$ApprovalDisabled) {
        $tdsApproved = $this->payroll_model->get_tds_approved_staff($data->staff_id, $data->schedule->financial_year_id);
        if (!$tdsApproved) {
            $PayslipStatus = '<span style="color:red">TDS not approved</span>';
            $disabled = 'disabled';
            $ApprovalDisabled = 'disabled';
        }
    }

    private function _construct_staff_payslip_data($salaryDataArray){
        $salaryData = '';
        $index = 1;
        $superAdmin = $this->authorization->isSuperAdmin();
        foreach ($salaryDataArray as $data) {
            if(empty($data->schedule)){
                return false;
            }
            $leave_info_new = $this->payroll_model->getStaffLeaveInfo($data->staff_id, $data->schedule->schdId);
            $show_biometric_data = $this->settings->getSetting('show_biometric_data_in_payroll');
            if($show_biometric_data) {
                $biometric_data = $this->payroll_model->getBiometricData($data->staff_id, $data->schedule->start_date, $data->schedule->end_date);
            }
            $leaveCount = 0;
            if(!empty($leave_info_new)){
                foreach ($leave_info_new as $key => $val) {
                    $leaveCount += $val->leave_count;
                }
            }

            $PayslipStatus = 'Not Generated';
            $approval_name = '';
            $no_of_present = $data->schedule->no_of_days - $leaveCount;
            $lop = $leaveCount;
            $disabled = '';
            $edit_disabled = '';
            $ApprovalDisabled = 'disabled';
            
            // if ($data->schedule->is_automatic_tds_calculation_enabled == 'Yes') {
            //     $this->handleAutomaticTDSCalculation($data, $PayslipStatus, $disabled, $ApprovalDisabled);
            // }
            if (!empty($data->payslip_data)) {
                $this->handlePayslipData($data, $PayslipStatus, $disabled, $edit_disabled, $ApprovalDisabled, $approval_name, $no_of_present, $lop);
            }

            $no_of_days = '';
            if($data->schedule){
                $no_of_days = $data->schedule->no_of_days;
            }

            $payslip_lop = '-';
            $totalEarnings = '-';
            $total_deduct = '-';
            $net_amount = '-';
            $no_of_payroll_days = '-';
            if(!empty($data->payslip_data)){
                $totalEarnings = $data->payslip_data->total_earnings;
                $total_deduct = $data->payslip_data->total_deductions;
                $net_amount = $data->payslip_data->total_earnings -  $data->payslip_data->total_deductions;
                $no_of_payroll_days = $data->payslip_data->no_of_days_present != null ? $data->payslip_data->no_of_days_present : '-';
                $payslip_lop = $data->payslip_data->lop != null ? $data->payslip_data->lop : '-';
            }

            if($superAdmin){
                $disabled = '';
                $edit_disabled = '';
            }
            $changeInStatus = ($data->staff_status != 'Approved') ? ' style="color:red"' : '';
            $salaryData .= "<tr>";
            $salaryData .= "<td>". $index++."</td>";
            $salaryData .= "<td><input ".$ApprovalDisabled." type='checkbox' id='approval_send_check_box_".$data->staff_id."' value='".$data->staff_id."' class='staff_ids_approval'></td>";
            $salaryData .= "<td><input ".$disabled." type='checkbox' id='mass_check_box_staff_id_".$data->staff_id."' value='".$data->staff_id."'  class='staff_ids'></td>";
            $salaryData .= "<td><a href='javascript:void(0)' style='".$edit_disabled."' onclick='edit_per_payslip(".$data->staff_id.")' id='payslipEditButton_".$data->staff_id."'data-toggle='modal' data-target='#edit_pay_slip' >" .$data->employee_code."</a></td>";
            $salaryData .= "<td>".$data->staff_name."</td>";
            $salaryData .= "<td".$changeInStatus.">".$data->staff_status."</td>";
            $salaryData .= "<td id='payslipStatus_$data->staff_id'>".$PayslipStatus.' '.$approval_name."</td>";
            $salaryData .= "<td>".(isset($data->salary_data) && isset($data->salary_data->slab_name) ? $data->salary_data->slab_name : 'Consultant' )."</td>";
            $salaryData .= "<td>".$data->doj."</td>";
            $salaryData .= "<td>".$no_of_days."</td>";
            $salaryData .= "<td>".$data->schedule->no_of_payroll_days."</td>";
            $salaryData .= "<td id='totalLop_$data->staff_id'>".$payslip_lop."</td>";
            $salaryData .= "<td id='noOfPayrollDays_$data->staff_id'>".$no_of_payroll_days."</td>";
            $salaryData .= "<td>".(isset($data->salary_data) && isset($data->salary_data->monthly_gross) ? $data->salary_data->monthly_gross : '0')."</td>";
            $salaryData .= "<td id='totalEarnings_$data->staff_id' >".$totalEarnings."</td>";
            $salaryData .= "<td id='totalDeduct_$data->staff_id'>".$total_deduct."</td>";
            $salaryData .= "<td id='totalNetAmount_$data->staff_id'>".round($net_amount, 2)."</td>";
            $salaryData .= "</tr>";
        }
        return $salaryData;
    }

    public function mass_payroll_generate_new(){
        $staff_id = $_POST['staff_id'];
        $selected_schedule = $_POST['selected_schedule'];
        $result = $this->payroll_model->generate_monthly_payslip($staff_id, $selected_schedule);
        $employmentType = $result->staff_details->employment_type;
        if(empty($employmentType))
            $employmentType = 'Regular';
        $leave_info_new = $this->payroll_model->getStaffLeaveInfo($staff_id, $selected_schedule);
        $leaveCount = 0;
        if(!empty($leave_info_new)){
            foreach ($leave_info_new as $key => $val) {
                $leaveCount += $val->leave_count;
            }
        }
        $csv_addition_data = $this->payroll_model->get_csv_additional_data_imported($staff_id, $selected_schedule);
        // echo "<pre>";print_r($result);die();
        $numberofPresentDays = $result->schedule->no_of_payroll_days - $leaveCount;
        if(!empty($csv_addition_data->lop) && $csv_addition_data->lop != 0){
            $numberofPresentDays = $result->schedule->no_of_payroll_days - $csv_addition_data->lop;
        }
        $salary_data  = (array) $result->salary_data;
        $payroll_data  = (array) $result->payslip_data;
        $payroll_settings  = $result->slab_setting;
        $loan_repayment  = $result->loan_repayment;
        $age_pt_cal= $result->staff_details->age_above_60;

        $payrollDataArry = $salary_data;
        if(!empty($payroll_data)){
            $payrollDataArry = $payroll_data;
        }
        $csvData = (array) $csv_addition_data;
        $mergeArry = array_merge($payrollDataArry, $csvData);
        
        $salary_data_merge =  $this->merge_imported_csv_edit_payslip_data($mergeArry, $csv_addition_data);
        $salary_data_merge['staff_id'] = $staff_id;
        // $salary_data_merge =  $this->merge_imported_csv_edit_payslip_data($payrollDataArry, $csv_addition_data);
        if($employmentType == 'Consultant'){
            $cal_payroll_data = $this->_calculate_payroll_monthly_for_consultant($salary_data_merge, $payroll_settings, $result->schedule->no_of_days, $numberofPresentDays, $salary_data, $loan_repayment, $age_pt_cal);
        } else {
            $cal_payroll_data = $this->_calculate_payroll_monthly($salary_data_merge, $payroll_settings, $result->schedule->no_of_days, $numberofPresentDays, $salary_data, $loan_repayment,$age_pt_cal, $result->schedule->is_automatic_tds_calculation_enabled);
        }
        if($employmentType == 'Consultant'){
            $cal_payroll_data['basic'] = 0;
        } else {
            $cal_payroll_data['basic'] = $cal_payroll_data['monthly_basic_salary'];
        }
        $cal_payroll_data['hra'] = $cal_payroll_data['staff_hra'];
        $cal_payroll_data['da'] = isset($cal_payroll_data['staff_da']) ? $cal_payroll_data['staff_da'] : 0 ;
        $tax_per_month = $cal_payroll_data['tds'];
        if($result->schedule->is_automatic_tds_calculation_enabled == 'Yes' && $employmentType != 'Consultant'){
            $remaningMonth =  $this->check_total_month_of_financial_year($result->schedule->fyear_from_date, $result->schedule->fyear_to_date, $result->schedule->start_date);
            $income = [];
            $income['ctc_with_employee_pf'] = $cal_payroll_data['monthly_ctc'];
            $income['basic_salary_with_da'] = $cal_payroll_data['monthly_basic_salary'];
            $income['hra'] = $cal_payroll_data['staff_hra'];
            $income['employee_pf_contribution'] = $cal_payroll_data['pf_for_employer'];
            $income['outside_ctc_allowance'] = $cal_payroll_data['total_outside_ctc'];
            $income['additional_month_allowance'] = $cal_payroll_data['total_additional_income'];
            $income['professional_tax'] = $cal_payroll_data['professional_tax'];
            $income['vpf'] = isset($cal_payroll_data['vpf']) ? $cal_payroll_data['vpf'] : 0;
            $tax_per_month = $this->payroll_model->get_monthly_payslip_tax_calucation($staff_id, $result->schedule->financial_year_id, $income, $remaningMonth);
            //echo "<pre>"; print_r($tax_per_month);die();
            $cal_payroll_data['total_deductions'] = $cal_payroll_data['total_deductions'] + $tax_per_month;
        }
        $cal_payroll_data['tds'] = $tax_per_month;
        // $cal_payroll_data['lop'] = $staff_id;
        $cal_payroll_data['working_days'] = $result->schedule->no_of_days;
        $cal_payroll_data['no_of_present_days'] = $numberofPresentDays;
        // $cal_payroll_data['selected_schedule'] = $selected_schedule;
        $respone =  $this->payroll_model->insert_monthly_payroll_data($staff_id, $selected_schedule, $cal_payroll_data, $numberofPresentDays);
        echo json_encode($respone);
    }

    private function check_total_month_of_financial_year($financial_year_start_date, $financial_year_end_date, $select_month){
        $financial_year_start_date = new DateTime($financial_year_start_date);
        $financial_year_end_date = new DateTime($financial_year_end_date);
        $start_month = new DateTime($select_month);
        // Calculate the number of remaining months from the selected start month
        $remaining_months_from_start = ($financial_year_end_date->format('Y') - $start_month->format('Y')) * 12 + 
            ($financial_year_end_date->format('m') - $start_month->format('m')) + 1;
        
        return $remaining_months_from_start;
    }

    public function merge_imported_csv_salary_data($salary_data, $csv_addition_data){
        $salary_data_array = (array) $salary_data;       

        $csv_data = (array)$csv_addition_data;
        
        // Trim the keys of the array
        $trimmed_keys =  $this->trim_array_keys($csv_data);

        // Create a new array with trimmed keys
        $csv_array = array_combine($trimmed_keys, $csv_data);

        $merge = array_merge($salary_data_array, $csv_array);
        return $merge;
    }
    
    private function trim_array_keys($array){
        return array_map('trim', array_keys($array));
    }

    private function _calculate_payroll_monthly($payrollData, $result, $no_of_days, $numberofPresentDays, $salary_data, $loanRepayment, $age_pt_cal, $is_automatic_tds_calculation_enabled) {
        $monthly_basic_salary = 0;
        $academic_grade_pay = isset($salary_data['academic_grade_pay']) ? $salary_data['academic_grade_pay'] : 0; 
        $staff_hra = isset($salary_data['staff_hra']) ? $salary_data['staff_hra'] : 0; 
        $staff_da = isset($salary_data['staff_da']) ? $salary_data['staff_da'] : 0; 
        $medical_allowance = isset($salary_data['medical_allowance']) ? $salary_data['medical_allowance'] : 0; 
        $cca = isset($salary_data['cca']) ? $salary_data['cca'] : 0; 
        $lta = isset($salary_data['lta']) ? $salary_data['lta'] : 0; 
        $conveyance = isset($salary_data['conveyance']) ? $salary_data['conveyance'] : 0; 
        $transport_allowance = isset($salary_data['transport_allowance']) ? $salary_data['transport_allowance'] : 0;
        $salary_pf_for_employer = isset($salary_data['pf_for_employer']) ? $salary_data['pf_for_employer'] : 0;
        $salary_pf = isset($salary_data['pf']) ? $salary_data['pf'] : 0;
        $academicgradepay =   round(($academic_grade_pay /  $no_of_days * $numberofPresentDays) / 100, 2);
        $hra = 0;
        $da = 0;
        $ma_slab = 0;
        $cca_slab = 0;
        $staffta = 0;
        $pf = 0;
        $pf_for_employer = 0;
        $ltaCal = 0;
        $ltaRules = 0;
        $sa = 0;

        $monthly_gross = round(($salary_data['monthly_gross'] /  $no_of_days * $numberofPresentDays), 2);
        // Calculate monthly basic salary based on the basic mode
        $monthly_basic_salary = round(($salary_data['monthly_basic_salary'] /  $no_of_days * $numberofPresentDays), 2);
        $hra = round((($staff_hra  + $academicgradepay) /  $no_of_days * $numberofPresentDays), 2);


        $da = round(($staff_da + $academicgradepay) / $no_of_days * $numberofPresentDays, 2);
        
        $ma_slab = 0;
        // Calculate Medical Allowance slab
        if ($result->medical_allowance_rules == '1') {
            $ma_slab = $medical_allowance;
        }
        if($result->medical_allowance_rules == '2'){
            if($monthly_basic_salary > 20000){
                $ma_slab = $medical_allowance;
            }
        }
        
        if($result->medical_allowance_rules == '3'){
            if($monthly_gross > 20000){
                $ma_slab = $medical_allowance;
            }
        }

        $cca_slab = round(($cca) / $no_of_days * $numberofPresentDays, 2);

        // Calculate staff TA
        // $staffta = round($monthly_gross - $monthly_basic_salary - $hra - $da - $ma_slab - $cca_slab - $result->transport_allowance, 2);

        // Calculate Professional Tax (PT)
        // echo "<pre>";print_r($monthly_gross);die();
        $state =  json_decode($this->settings->getSetting('State Wise PT'));
        // if ($monthly_gross >= 25000 && $age_pt_cal == 1) {
        //     $pt = 200;
        // } else {
        //     $pt = 0;
        // }
        // if(!empty($state)){
        //     $pt = $this->_calculate_state_wise_pt($state, $age_pt_cal, $monthly_gross, $payrollData['staff_id']);
        // }

        $da_basic_salary = $monthly_basic_salary + $da;

        // Rules
        // Rule 1 - If Basic + DA > ₹15,000: ₹1,800, otherwise 12% of Basic salary.
        // Rule 2 - If Basic + DA > ₹15,000: 12%, otherwise 12% of Basic salary.
        // Rule 3 - No contribution (₹0).
        // Rule 4 - If Basic + DA > ₹25,000: ₹1,800, otherwise 10% of Basic salary.
        // Rule 5 - Salary PF calculated for present days.
        // Rule 6 - 12% of ₹15,000 if Basic + DA ≥ ₹15,000, otherwise 12% of Basic + DA.
        // Rule 7 - ₹1,800 if Basic + DA ≥ ₹15,000, otherwise Salary PF for present days.

        $pf = 0;
        // Calculate PF
        if ($result->pf == '1.00') {
            $pf = ($da_basic_salary >= 15000) ? 1800 : round(($da_basic_salary * 12) / 100, 2);
        } elseif ($result->pf == '2.00') {
            $pf = round(($da_basic_salary * 12) / 100, 2);
        } elseif ($result->pf == '3.00') {
            $pf = 0;
        } elseif ($result->pf == '4.00') {
            $pf = ($da_basic_salary >= 25000) ? 1800 : round(($da_basic_salary * 10) / 100, 2);
        }else if($result->pf == '5.00'){
            $pf = round(($salary_pf /  $no_of_days * $numberofPresentDays), 2); // Get from salary structure for rull number pf 5 
        }else if($result->pf == '6.00'){
            $pf = ($da_basic_salary >= 15000) ? round((15000 * 12) / 100, 2) : round(($da_basic_salary * 12) / 100, 2);
        }else if($result->pf == '7.00'){
            $pf = ($da_basic_salary >= 15000) ? 1800 : round(($salary_pf /  $no_of_days * $numberofPresentDays), 2);
        }

        // Calculate PF for Employer
        if ($result->pf_for_employer == '1.00') {
            $pf_for_employer = ($da_basic_salary >= 15000) ? 1800 : round(($da_basic_salary * 12) / 100, 2);
        } elseif ($result->pf_for_employer == '2.00') {
            $pf_for_employer = round(($da_basic_salary * 12) / 100, 2);
        } elseif ($result->pf_for_employer == '3.00') {
            $pf_for_employer = 0;
        } elseif ($result->pf_for_employer == '4.00') {
            $pf_for_employer = ($da_basic_salary >= 25000) ? 1800 : round(($da_basic_salary * 10) / 100, 2);
        }else if($result->pf_for_employer == '5.00'){
            $pf_for_employer = round(($salary_pf_for_employer /  $no_of_days * $numberofPresentDays), 2); // Get from salary structure for rull number pf 5 
        }else if($result->pf_for_employer == '6.00'){
            $pf_for_employer = ($da_basic_salary >= 15000) ? round((15000 * 13) / 100, 2) : round(($da_basic_salary * 13) / 100, 2);
        }else if($result->pf_for_employer == '7.00'){
            $pf_for_employer = ($da_basic_salary >= 15000) ? 1800 : round(($salary_pf /  $no_of_days * $numberofPresentDays), 2);
        }
        // echo "<br><pre>";print_r($pf);
        // echo "<br><pre>";print_r($pf_for_employer);
        
        $ltaCal = round(($lta /  $no_of_days * $numberofPresentDays), 2);

        $ma_slab = round(($ma_slab /  $no_of_days * $numberofPresentDays), 2);

        $ltaRules = 0;
        if ($result->lta_rules == '1') {
            $ltaRules = $ltaCal;
        }
        if ($result->lta_rules == '2') {
            if($monthly_gross > 35000){
                $ltaRules = $ltaCal;
            } 
        }
        if ($result->lta_rules == '3') {
            if($salary_data['monthly_gross'] >= 27500){
                $ltaRules = $ltaCal;
            } 
        }

        if ($result->lta_rules == '5') {
            if ($result->lta_slab == 'percentage') {
                if($da_basic_salary >= 27500){
                    $ltaRules = round(($da_basic_salary * $result->lta) / 100, 2);
                }else{
                    $ltaRules = 0;
                }
            } else {
                $ltaRules = $result->lta;
            }
        }

        $get_columnspf = $this->payroll_model->get_payroll_column_table();
        $transport_allowance =  round(($transport_allowance + $academicgradepay) / $no_of_days * $numberofPresentDays, 2);
        $conveyance =  round($conveyance  / $no_of_days * $numberofPresentDays, 2);
        $sa_pf_employer = 0;
        $ta = 0;

        foreach ($get_columnspf->payroll_column as $key => $val) {
            if($val->column_name == 'pf_employee_contribution' && $val->calculate_number_of_days_present == 1){
                $pf = ($pf == '1800') ? round(($pf /  $no_of_days * $numberofPresentDays), 2) : $pf;
            }
            if($val->column_name == 'pf_for_employer' && $val->calculate_number_of_days_present == 1){
                $pf_for_employer = ($pf_for_employer == '1800') ? round(($pf_for_employer /  $no_of_days * $numberofPresentDays), 2) : $pf_for_employer;
            }
            if ($val->include_ctc == 1 &&  $val->column_name =='pf_for_employer') {
                $sa_pf_employer =  $pf_for_employer;
            }
            if ($val->include_ctc == 1 &&  $val->column_name == 'transport_allowance') {
                $ta =  $transport_allowance;
            }
        }

        // Calculate Special Allowance (SA)
        $sa = round($monthly_gross - $monthly_basic_salary - $hra - $da - $conveyance - $ma_slab - $ltaRules - $cca_slab - $staffta - $sa_pf_employer - $ta, 2);

        $esi = 0;
        if ($result->esi == 1) {
            $esi = 0;
        }else if ($result->esi == '2.00'){
            $esi = round($monthly_gross*0.75/100, 2);
        }else if($result->esi == '3.00'){
            $esi = round($monthly_gross*1.75/100,2);
        }else if($result->esi == '4.00'){
            if (round($salary_data['monthly_gross']) < '21000.00') {
                $esi = round($monthly_gross*0.75/100,2);
            }else{
                $esi = 0;
            }
        }

        $columns = $this->payroll_model->get_payroll_column_table();
        $earnings = [];
        $deduction = [];
        $salaryData = [];
        foreach ($columns->payroll_column as $key => $val) {
            if($val->type == 'earnings'){
                array_push($earnings, $val);
            }
            if($val->type == 'deduction'){
                array_push($deduction, $val);
            }
            if($val->salary_structure == 1){
                if (isset($salary_data[$val->column_name])) {
                    $salaryData[$val->column_name] = $salary_data[$val->column_name];
                }
            }
        }

        // Return calculated values
        $dataPyaroll =  array(
            'monthly_basic_salary' => $monthly_basic_salary,
            'staff_hra' => $hra,
            'staff_da' => $da,
            'medical_allowance' => $ma_slab,
            'cca' => $cca_slab,
            'staff_ta' => $staffta,
            'professional_tax' => 0,
            'pf_employee_contribution' => $pf,
            'esi' => $esi,
            'pf_for_employer' => $pf_for_employer,
            'lta' => $ltaRules,
            'special_allowance' => $sa,
            'transport_allowance'=>$transport_allowance,
            'conveyance' => $conveyance,
            'actual_basic_salary'=> $salary_data['monthly_basic_salary'],
            'loan_repayment' => empty($loanRepayment) ? ($payrollData['loan_repayment'] ?? 0) : $loanRepayment,
            'monthly_ctc' => $monthly_gross
        );
        $mergeSalaryPayroll = array_merge($payrollData, $salaryData);
        $difference = array_diff_key($mergeSalaryPayroll, $dataPyaroll);

        foreach ($earnings as $obj) {
            if ($obj->calculate_number_of_days_present == 1) {
                $key = $obj->column_name;
                if (isset($difference[$key])) {
                    $difference[$key] =  round(($difference[$key]) / $no_of_days * $numberofPresentDays, 2);
                    // $dataPyaroll['special_allowance'] = $sa - $difference[$key];
                }
            }
        }

        $merge_dataPyaroll = array_merge($dataPyaroll, $difference);
        $merge_dataPyaroll['total_earnings'] = 0;
        $merge_dataPyaroll['total_deductions'] = 0;
        $merge_dataPyaroll['total_outside_ctc'] = 0;
        $merge_dataPyaroll['total_additional_income'] = 0;
        foreach ($earnings as $obj) {
            if ($obj->total_earings_include == 1) {
                $key = $obj->column_name;
                if (isset($merge_dataPyaroll[$key])) {
                    $merge_dataPyaroll['total_earnings'] += floatval($merge_dataPyaroll[$key]);
                }
            }

            if ($obj->outside_ctc_salary_strucutre == 1) {
                $key = $obj->column_name;
                if (isset($merge_dataPyaroll[$key])) {
                    $merge_dataPyaroll['total_outside_ctc'] += floatval($merge_dataPyaroll[$key]);
                }
            }

            if ($obj->additional_income_monthly_strcuture == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                $key = $obj->column_name;
                if (isset($merge_dataPyaroll[$key])) {
                    $merge_dataPyaroll['total_additional_income'] += floatval($merge_dataPyaroll[$key]);
                }
            }
        }
        
        $merge_dataPyaroll['professional_tax'] = $this->_calculate_state_wise_pt($state, $age_pt_cal, $merge_dataPyaroll['total_earnings'], $payrollData['staff_id']);

        foreach ($deduction as $obj) {
            if ($obj->total_deduction_include == 1) {
                $key = $obj->column_name;
                if (isset($merge_dataPyaroll[$key])) {
                    if (!($is_automatic_tds_calculation_enabled === 'Yes' && $key === 'tds')) {
                        $merge_dataPyaroll['total_deductions'] += floatval($merge_dataPyaroll[$key]);
                    }
                }
            }
        }

        // echo "<pre>";print_r($merge_dataPyaroll);die();
        return $merge_dataPyaroll;
    }

    public function insert_update_payroll_salary(){
        if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
            redirect('dashboard', 'refresh');
        }
        $input = $this->input->post();
        $staff_id = $this->input->post('staff_id');
        $old_value = $this->input->post('old_value');
        $new_value = $this->input->post('new_value');
        $source = 'Salary Structure New';
        if(!empty($old_value) || !empty($new_value)){
            $this->payroll_model->store_payroll_edit_history($staff_id,$old_value,$new_value, $source);
        }
        $result = $this->payroll_model->insert_update_payroll_salary($input);
        if($result){
            $this->session->set_flashdata('flashSuccess', 'Data Upload Successful');
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong.');
        }
        redirect("management/payroll/showPayrollData");
    }

    private function _calculate_payroll_monthly_for_consultant($salary_data_merge, $payroll_settings, $no_of_days, $numberofPresentDays, $salary_data, $loanRepayment,$age_pt_cal){
        $get_columnspf = $this->payroll_model->get_payroll_column_table();
        $numberofPresentDays = min($numberofPresentDays, $no_of_days);

        $totalEarnings = 0;
        $totalDeductions = 0;
        $totalOutsideCTC = 0;
        $totalAdditionalIncome = 0;

        $earnings = [];
        $deductions = [];
        $finalPayroll = [];
        $salaryData = [];

        // Collect salary structure values
        foreach ($get_columnspf->payroll_column as $val) {
            if ($val->is_consultant == 1 && $val->salary_structure == 1) {
                $key = $val->column_name;
                $salaryData[$key] = isset($salary_data[$key]) ? $salary_data[$key] : 0;
            }
        }

        $actualBasicSalary = ['actual_basic_salary' => $salary_data['monthly_basic_salary'] ? $salary_data['monthly_basic_salary'] : 0];
        $mergeSalaryPayroll = array_merge($salary_data_merge, $salaryData);
        $difference = array_diff_key($mergeSalaryPayroll, $actualBasicSalary);

        // Clear any previously set values to prevent double addition
        foreach ($get_columnspf->payroll_column as $col) {
            $key = $col->column_name;
            unset($salary_data_merge[$key]);
        }

        foreach ($get_columnspf->payroll_column as $col) {
            if ($col->is_consultant != 1) continue;

            $key = $col->column_name;
            $amount = isset($mergeSalaryPayroll[$key]) ? floatval($mergeSalaryPayroll[$key]) : 0;
            if ($amount == 0) continue;

            $shouldProrate = ($col->calculate_number_of_days_present == 1);
            $finalAmount = ($shouldProrate && $no_of_days > 0) ? round(($amount / $no_of_days) * $numberofPresentDays, 2) : $amount;
            $finalPayroll[$key] = $finalAmount;

            if ($col->type == 'earnings') {
                $earnings[] = $col;
                if ($col->total_earings_include == 1) {
                    $totalEarnings += $finalAmount;
                }
                if ($col->outside_ctc_salary_strucutre == 1) {
                    $totalOutsideCTC += $finalAmount;
                }
                if ($col->additional_income_monthly_strcuture == 1 && !in_array($key, ['reimbursement', 'rent_reimbursment'])) {
                    $totalAdditionalIncome += $finalAmount;
                }
            } elseif ($col->type == 'deduction') {
                $deductions[] = $col;
                if ($col->total_deduction_include == 1 && $col->column_name != 'tds') {
                    $totalDeductions += $finalAmount;
                }
            }
        }

        // Calculate TDS (10% of total earnings)
        $tds = round($totalEarnings * 0.10, 2);
        $finalPayroll['tds'] = $tds;
        $totalDeductions += $tds;

        // Add loan repayment if applicable
        $loanRepaymentAmount = floatval($loanRepayment);
        if ($loanRepaymentAmount > 0) {
            $finalPayroll['loan_repayment'] = $loanRepaymentAmount;
            $totalDeductions += $loanRepaymentAmount;
        }

        // Calculate net pay
        $netPay = $totalEarnings - $totalDeductions;
        $mergedFinal = array_merge($salary_data_merge, $finalPayroll, $actualBasicSalary, [
            'total_earnings' => $totalEarnings,
            'total_deductions' => $totalDeductions,
            'total_outside_ctc' => $totalOutsideCTC,
            'total_additional_income' => $totalAdditionalIncome,
            'monthly_ctc' => $totalEarnings,
            'net_pay' => $netPay
        ]);
        return $mergedFinal;
    }

    public function edit_mass_payslip_staff_wise(){
        $staff_id = $_POST['staff_id'];
        $selected_schedule = $_POST['selected_schedule'];
        $no_of_present_days = $_POST['no_of_present_days'];
        
        $result = $this->payroll_model->edit_mass_payslip_staff_wise($staff_id, $selected_schedule);
        $employmentType = $result->staff_details->employment_type;
        if(empty($employmentType))
            $employmentType = 'Regular';
        $leave_info_new = $this->payroll_model->getStaffLeaveInfo($staff_id, $selected_schedule);
        $show_biometric_data = $this->settings->getSetting('show_biometric_data_in_payroll');
        if($show_biometric_data) {
            $biometric_data = $this->payroll_model->getBiometricData($staff_id, $result->schedule->start_date, $result->schedule->end_date);
        }
        $leaveCount = 0;
        if(!empty($leave_info_new)){
            foreach ($leave_info_new as $key => $val) {
                $leaveCount += $val->leave_count;
            }
        }
        $payroll_data  = (array) $result->payslip_data;
        $salary_data  = (array) $result->salary_data;
        $loanRepayment  = $result->loan_repayment;
        $payroll_settings  = $result->slab_setting;
        $age_pt_cal= $result->staff_details->age_above_60;
        $numberofPresentDays = $result->schedule->no_of_payroll_days - $leaveCount;

        $payrollDataArry = $salary_data;
        if(!empty($payroll_data)){
            $payrollDataArry = $payroll_data;
            $numberofPresentDays = $payroll_data['no_of_days_present']; 
        }
        $csv_addition_data = $this->payroll_model->get_csv_additional_data_imported($staff_id, $selected_schedule);
        if(!empty($csv_addition_data->lop) && $csv_addition_data->lop != 0){
            $numberofPresentDays = $result->schedule->no_of_payroll_days - $csv_addition_data->lop;
        }
        $csvData = (array) $csv_addition_data;
        $mergeArry = array_merge($payrollDataArry, $csvData);
        $salary_data_merge =  $this->merge_imported_csv_edit_payslip_data($mergeArry,$csv_addition_data);
        $salary_data_merge['staff_id'] = $staff_id;
        if(!empty($no_of_present_days)){
            $numberofPresentDays = $no_of_present_days; 
        }
        $UpdateButtonDisplayNone = 0;
        if($result->schedule->is_automatic_tds_calculation_enabled =='Yes'){
            $UpdateButtonDisplayNone = 1;
            $tdsApproved = $this->payroll_model->get_tds_approved_staff($staff_id, $result->schedule->financial_year_id);
            if(!$tdsApproved){
                $UpdateButtonDisplayNone = 1;
            }else{
                $UpdateButtonDisplayNone = 0;
            }
        }
        if($employmentType == 'Consultant'){
            $cal_payroll_data = $this->_calculate_payroll_monthly_for_consultant($salary_data_merge, $payroll_settings, $result->schedule->no_of_days, $numberofPresentDays, $salary_data, $loanRepayment, $age_pt_cal);
        } else {
            $cal_payroll_data = $this->_calculate_payroll_monthly($salary_data_merge, $payroll_settings, $result->schedule->no_of_days, $numberofPresentDays, $salary_data, $loanRepayment,$age_pt_cal, $result->schedule->is_automatic_tds_calculation_enabled);
        }
        $columnArray = $this->payroll_model->get_payroll_column_table();
        $earnings = [];
        $deduction = [];
        if(!empty($columnArray)){
            $earnings = [];
            $deduction = [];
            foreach ($columnArray->payroll_column as $key => $val) {
                $isConsultantColumn = isset($val->is_consultant) && $val->is_consultant == '1';
                $isEarning = ($val->type == 'earnings' && $val->monthly_structure == '1');
                $isDeduction = ($val->type == 'deduction' && $val->monthly_structure == '1');

                // For Consultant staff, include only consultant-specific columns
                // For all others, include all columns (regardless of is_consultant)
                if ($employmentType == 'Consultant') {
                    if ($isEarning && $isConsultantColumn) {
                        $earnings[] = $val;
                    }
                    if ($isDeduction && $isConsultantColumn) {
                        $deduction[] = $val;
                    }
                } else {
                    if ($isEarning) {
                        $earnings[] = $val;
                    }
                    if ($isDeduction) {
                        $deduction[] = $val;
                    }
                }
            }

            usort($earnings, function($a, $b) {
                $orderA = ($a->order_by === "") ? PHP_INT_MAX : $a->order_by;
                $orderB = ($b->order_by === "") ? PHP_INT_MAX : $b->order_by;
                return $orderA - $orderB;
            });
        }
        $data['earnings'] = $earnings;
        $data['deduction'] = $deduction;
        echo json_encode(array('earnings'=>$earnings, 'deduction'=>$deduction, 'cal_payroll_data'=>$cal_payroll_data,'no_of_days'=>$result->schedule->no_of_days,'no_of_present_days'=>$numberofPresentDays,'leave_info_new'=>$leave_info_new,'leave_schedule_name'=>$result->schedule->schedule_name,'UpdateButtonDisplayNone'=>$UpdateButtonDisplayNone));
    }

    private function merge_imported_csv_edit_payslip_data($payrollDataArry,$csv_addition_data){
        $csvData = (array) $csv_addition_data;
        foreach ($payrollDataArry as $key => $value) {
            if ((empty($value) || $value === '' || $value == '0.00' || $value == '0') && isset($csvData[$key])) {
                $payrollDataArry[$key] = $csvData[$key];
            }
        }    
        return  $payrollDataArry; 
    }

    public function individual_staff_payslip_generate(){
        $input = $this->input->post();
        // echo "<pre>";print_r($input);die();
        $staff_id = $input['edit_payslip_staff_id'];
        $selected_schedule =  $input['selected_schedule'];
        $numberofPresentDays = $input['no_of_present_days'];
        $input['basic'] = $input['monthly_basic_salary'];
        $input['total_deductions'] = $input['total_deduct'];
        $input['hra'] = $input['staff_hra'];
        $input['da'] = isset($input['staff_da']) ? $input['staff_da'] : 0 ;
        $cal_payroll_data = $input;
        $respone =  $this->payroll_model->insert_monthly_payroll_data($staff_id, $selected_schedule, $cal_payroll_data, $numberofPresentDays);
        $staff_id = $this->input->post('edit_payslip_staff_id');
        $old_value = $this->input->post('old_value');
        $new_value = $this->input->post('new_value');
        $source = 'Mass Payslip New';
        if(!empty($old_value) && !empty($new_value)){
            $this->payroll_model->store_payroll_edit_history($staff_id,$old_value,$new_value, $source);
        }
        echo json_encode($respone);
    }

    public function edit_mass_earnings_changes_payslip_staff_wise(){
        $staff_id = $_POST['staff_id'];
        $selected_schedule = $_POST['selected_schedule'];
        $numberofPresentDays = $_POST['no_of_present_days'];
        $monthly_gross = $_POST['monthly_gross'];
        
        $salary_data_merge = $_POST['inputArry'];
        $salary_data_merge['monthly_gross'] = $_POST['monthly_gross'];
        $result = $this->payroll_model->edit_mass_payslip_staff_wise($staff_id, $selected_schedule);
        $employmentType = $result->staff_details->employment_type;
        if(empty($employmentType))
            $employmentType = 'Regular';
        $payroll_settings  = $result->slab_setting;
        $salary_data  = (array) $result->salary_data;
        $loan_repayment  = $result->loan_repayment;
        $age_pt_cal= $result->staff_details->age_above_60;
        $salary_data_merge['staff_id'] = $staff_id;

        if($employmentType == 'Consultant'){
            $cal_payroll_data = $this->_calculate_payroll_monthly_for_consultant($salary_data_merge, $payroll_settings, $result->schedule->no_of_days, $numberofPresentDays, $salary_data, $loan_repayment, $age_pt_cal);
        } else {
            $cal_payroll_data = $this->_calculate_payroll_monthly($salary_data_merge, $payroll_settings, $result->schedule->no_of_days, $numberofPresentDays, $salary_data, $loan_repayment,$age_pt_cal, $result->schedule->is_automatic_tds_calculation_enabled);
        }
        $columnArray = $this->payroll_model->get_payroll_column_table();
        if(!empty($columnArray)){
            $earnings = [];
            $deduction = [];
            foreach ($columnArray->payroll_column as $key => $val) {
                $isConsultantColumn = isset($val->is_consultant) && $val->is_consultant == '1';
                $isEarning = ($val->type == 'earnings' && $val->monthly_structure == '1');
                $isDeduction = ($val->type == 'deduction' && $val->monthly_structure == '1');

                // For Consultant staff, include only consultant-specific columns
                // For all others, include all columns (regardless of is_consultant)
                if ($employmentType == 'Consultant') {
                    if ($isEarning && $isConsultantColumn) {
                        $earnings[] = $val;
                    }
                    if ($isDeduction && $isConsultantColumn) {
                        $deduction[] = $val;
                    }
                } else {
                    if ($isEarning) {
                        $earnings[] = $val;
                    }
                    if ($isDeduction) {
                        $deduction[] = $val;
                    }
                }
            }

            usort($earnings, function($a, $b) {
                $orderA = ($a->order_by === "") ? PHP_INT_MAX : $a->order_by;
                $orderB = ($b->order_by === "") ? PHP_INT_MAX : $b->order_by;
                return $orderA - $orderB;
            });
        }
        $data['earnings'] = $earnings;
        $data['deduction'] = $deduction;
        echo json_encode(array('earnings'=>$earnings, 'deduction'=>$deduction, 'cal_payroll_data'=>$cal_payroll_data,'no_of_days'=>$result->schedule->no_of_days,'no_of_present_days'=>$numberofPresentDays));
    }

    public function get_re_payroll_increment_approval_cycle_type(){
        $incrementFrequency = $_POST['incrementFrequency'];
        $allowed = ['monthly', 'yearly'];
        if(!in_array($incrementFrequency, $allowed)){
            echo json_encode(array('error'=>'Please Set The Increment Frequency.'));
            return;
        }
        if(trim($incrementFrequency) == ''){
            echo json_encode(array('error'=>'Please Set The Increment Frequency.'));
            return;
        }
        $staff_id = isset($_POST['staff_id']) ? $_POST['staff_id'] : 0;
        $result = $this->payroll_model->get_payroll_re_increment_data_matching_with_salary_structure($_POST['id']);
        $monthly_gross = 0;
        $slabSettings = [];
        if(!empty($result->salary_data)){
            if (trim($incrementFrequency) == 'monthly') {
                $monthly_gross = $result->salary_data->monthly_gross + $result->total_amount;
                // $result->salary_data->yearly_ctc = $monthly_gross * 12;
            } else {
                $yearCTC = $result->salary_data->yearly_ctc + $result->total_amount;
                $monthly_gross = $yearCTC / 12;
            }
        }
        if(!empty($result->slab_setting)){
            unset($result->salary_data->lta);
            unset($result->salary_data->pf_for_employer);
            unset($result->salary_data->pf);
            unset($result->salary_data->cca);
            $slabSettings = $obj_merged = (object) array_merge((array) $result->slab_setting, (array) $result->salary_data);
        }
        if (is_object($slabSettings)) {
            $slabSettings->staff_id = $staff_id;
        } else {
            $slabSettings['staff_id'] = $staff_id;
        }
        $age_pt_cal= $result->staff_age->age_above_60;
        $calResult = $this->_calculate_payroll_salary($monthly_gross, $slabSettings, $age_pt_cal);
        echo json_encode(array('increment_salary'=>$calResult, 'total_mount' => $result->total_amount, 'effective_from' => $result->schedule_name, 'previous_salary_data'=>$result->salary_data,'staff_details'=>$result->staff_details,'staff_increment_cycle_id'=>$result->staff_increment_cycle_id));
    }

    public function submit_csv_salary_data_staff_wise(){
        $input = $this->input->post();
        $staffDetails = $this->payroll_model->getStaffName($input['staff_id']);
        if($staffDetails->employment_type == 'Consultant'){
            echo -1;
            exit();
        }
        $slabs = $input['slab_id'];
        if(empty($slabs)){
            echo -2;
            exit();
        }
        $monthly_gross = $input['salary_columns']['monthly_gross'];
        $monthly_basic_salary = (isset($input['salary_columns']['monthly_basic_salary'])) ? $input['salary_columns']['monthly_basic_salary'] : '0';
        $schoolName = $this->settings->getSetting('school_short_name');
        if ($schoolName != 'pnccicse' || $schoolName != 'pncc') {
            if(empty($monthly_gross) || $monthly_basic_salary == 0){
                echo -3;
                exit();
            }
        } else {
            if($monthly_basic_salary == 0){
                echo -3;
                exit();
            }
        }
        $result = $this->payroll_model->get_slab_settings_details($slabs);
        $staff_details = $this->payroll_model->getStaffname($input['staff_id']);
        $age_pt_cal= $staff_details->age_above_60;
        $slabSettings = (array) $result;
        $settingsMerge = json_decode(json_encode(array_merge($slabSettings, $input['salary_columns'])));
        
        $settingsMerge->staff_id = $input['staff_id'];
        if($schoolName != 'pnccicse' || $schoolName != 'pncc'){
            $calResult = $this->_calculate_payroll_salary($monthly_gross, $settingsMerge, $age_pt_cal, $monthly_basic_salary);
        }else{
            $calResult = $this->_calculate_payroll_salary_mannualy($settingsMerge);
        }
        $calResult['slab_id'] = $input['slab_id'];
        $calResult['staff_id'] = $input['staff_id'];
        echo $this->payroll_model->insert_update_payroll_salary($calResult, 'Mass Salary Upload');
    }

    public function income_declaration_cal(){
        $staffid = $_POST['staffid'];
        $financial_year_id = $_POST['financial_year_id'];
        $result = $this->payroll_model->getincome_declaration($staffid, $financial_year_id);
        echo json_encode($result);
    }

    public function payroll_column_settings(){
        $this->config->load('form_elements');
        $payroll_columns = $this->config->item('payroll_columns');
        $data['columns_db'] = $this->payroll_model->get_payroll_template();
        $columns = [];
        if(!empty($data['columns_db'])){
            if(!empty($data['columns_db']->payroll_json)){
                $jsondecode = json_decode($data['columns_db']->payroll_json, true);
                $columns =  $jsondecode['payroll_column'];
            }
        }
        $default_columns = ['monthly_gross', 'slab', 'yearly_gross', 'yearly_ctc', 'monthly_basic_salary'];
        foreach ($payroll_columns as $key => &$val) {
            $val['enabled_column'] = 0;
            $found = 0;
            if (!empty($columns)) {
                foreach ($columns as $col) {
                    if ($val['column_name'] == $col['column_name']) {
                        $val = $col;
                        $found = 1;
                        break;
                    }
                }
            }
            if(in_array($val['column_name'], $default_columns)){
                $val['default'] = 1;
            }else{
                $val['default'] = 0;
            }
            if ($found) {
                $val['enabled_column'] = 1; 
            }
        }
        unset($val);
        $data['payroll_columns'] = $payroll_columns;
        // $data['payroll_columns'] =  $this->mergeArrays($payroll_columns, $columns);
        // $data['main_content'] = 'management/payroll/column_json_setting';
        $data['main_content'] = 'management/payroll/column_json_setting_new';
        $this->load->view('inc/template', $data);
    }

    public function save_payroll_column_details(){
        $checked = $this->input->post('checked');
        $columns = $this->input->post('columns');
        $jsonArry['payroll_column'] = [];
        foreach ($columns as $val) {
            if (array_key_exists($val['column_name'], $checked) && $val['display_name'] !='') {
                $jsonArry['payroll_column'][] = $val;
            }
        }
        $json_data = json_encode($jsonArry, JSON_PRETTY_PRINT);
        $result = $this->payroll_model->save_payroll_fields_master_setting($json_data);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Data upload successfull.');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to update data.');

        }
        redirect('management/payroll/payroll_column_settings');
    }

    public function mergeArrays($array1, $array2){
        $mergedArray = array();

        $array2Assoc = array();
        foreach ($array2 as $item2) {
            $array2Assoc[$item2['column_name']] = $item2;
        }
        
        foreach ($array1 as $item1) {
            $columnName = $item1['column_name'];
            if (isset($array2Assoc[$columnName])) {
                $item2 = $array2Assoc[$columnName];
                foreach ($item2 as $key => $value) {
                    if (!empty($value)) {
                        $item1[$key] = $value;
                    }
                }
            }
            $mergedArray[] = $item1;
        }

        return $mergedArray;
    }

    public function re_generate_payslip($selected_month ='', $schedule_year = ''){

        $schedules = $this->payroll_model->get_schedules();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $present_month = (empty($schedules))?'':$schedules[0]['id'];

        $stafType = $this->input->post('staff_type');
        if (!empty($stafType)) {
            $stafType =$stafType;
        }else{
            $stafType='all';
        }

        foreach ($schedules as $key => &$val) {
            $val['scMonth'] = 'Not Selected';
            if (date('m',strtotime($val['start_date'])) == date('m', strtotime(date('Y-m')." -1 month"))) {
                $val['scMonth'] = 'Selected';
                $present_month = $val['id'];
            }
        }
        if (!empty($selected_month)) {
            $data['selected_schedule'] = $selected_month;
        }else{
            $data['selected_schedule'] = $present_month;
        }
        
        $schedule_year = (empty($data['financial_year']))?'':$data['financial_year'][0]->id;
        $currentYear = date('Y');
        $currentMonth = date('n');
        foreach ($data['financial_year'] as $key => $val) {
            if ($currentMonth <= 3) {
                if (date('Y', strtotime($val->from_date)) == ($currentYear - 1)) {
                    $schedule_year = $val->id;
                    break;
                }
            } else {
                if (date('Y', strtotime($val->from_date)) == $currentYear) {
                    $schedule_year = $val->id;
                    break;
                }
            }
        }
        $data['schedules'] = $schedules;
        $data['schedule_year'] = $schedule_year;
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['selected_staff_type'] = $stafType;
        $data['sStatus'] = $this->settings->getSetting('staff_status');
        $data['main_content'] = 'management/payroll/re_generate_payslip';
        $this->load->view('inc/template', $data);
    }

    public function get_payroll_data_for_regenerate_staff(){
        $selected_schedule = $_POST['selected_schedule'];
        $stafftypeId = $_POST['stafftypeId'];
        $payslip_data = $this->payroll_model->get_payroll_data_for_regenerate_staff($selected_schedule, $stafftypeId);
        $salaryData = [];
        if(!empty($payslip_data)){
            $salaryData =  $this->_construct_staff_re_generate_pdf_payslip_data($payslip_data,$selected_schedule);
        }
        echo json_encode($salaryData);
    }

    public function _construct_staff_re_generate_pdf_payslip_data($payslip_data,$selected_schedule){
        $salaryData = '';
        $index = 1;
        foreach ($payslip_data as $data) {
            $salaryData .= "<tr>";
            $salaryData .= "<td>". $index++."</td>";
            $salaryData .= "<td><div class='d-flex justify-content-between align-items-center'><input type='checkbox' id='pdf_regenerate_".$data->staff_id."' value='".$data->staff_id.'_'.$selected_schedule."' class='staff_ids_regenearte'><button onclick='download_pdf(".$data->staff_id.", ".$selected_schedule.")' class='btn btn-success'><i class='fa fa-download mr-0'></i></button></div></td>";
            $salaryData .= "<td>".$data->employee_code."</td>";
            $salaryData .= "<td>".$data->staff_name."</td>";
            $salaryData .= "<td>".$data->total_earnings."</td>";
            $salaryData .= "<td>".$data->total_deductions."</td>";
            $salaryData .= "<td>".($data->total_earnings - $data->total_deductions)."</td>";
            $salaryData .= "<td>".$data->old_monthly_ctc."</td>";
            $salaryData .= "<td>".$data->total_incremnts."</td>";
            $salaryData .= "<td>".$data->current_monthly_ctc."</td>";
            $salaryData .= "</tr>";
        }
        return $salaryData;
    }

    public function re_generate_staff_payslip_data(){
        $selected_month = $_POST['schedule_id'];
        $staffId = $_POST['staff_id'];
        $staffDetails = $this->payroll_model->getStaffname($staffId);
        $template = $this->payroll_model->get_email_template_for_payslip($staffDetails->employment_type);
        if($template){
            $data['payslip'] = $this->payroll_model->get_payslip_info($staffId, $selected_month);
            $pdf_html = $this->_create_template_payslip($data['payslip'],$template);
            $update =  $this->payroll_model->update_payroll_html_receipt($pdf_html, $staffId, $selected_month);
            $this->genearte_payslip_pdf($pdf_html, $staffId, $selected_month);
        }
    }

    public function apply_new_regime_to_staff_by_id(){
        $input = $this->input->post();
        $result = $this->payroll_model->apply_new_regime_to_staff_by_id($input);
        $status = $input['status'];
        if($status == 'Reopen Proof Submission'){
            $this->sendTaxRegimeConversionEmail($input['staff_id'], $input['financial_year']);
        }
        echo json_encode($result);
    }

    private function sendTaxRegimeConversionEmail($staffId, $financialYear){
        $financial_year_details = $this->payroll_model->get_financial_year_details($financialYear);
        $staff_details = $this->payroll_model->get_staff_details_for_email($staffId, $financialYear);
        $email_template = $this->payroll_model->get_email_template('payroll_convert_old_regime_to_new_regime');
        if (empty($email_template) || $staff_details->tds_status != 'Approved' || $staff_details->selected_regime != 2) {
            return;
        }
        $sent_by = $this->authorization->getAvatarStakeHolderId();

        $acad_year_id = $this->settings->getSetting('academic_year_id');
        $school_name = $this->settings->getSetting('school_name');

        $email_data = [];
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $staff_details->staff_id;
        $email_obj->avatar_type = $staff_details->avatar_type;
        $email_obj->email = $staff_details->to_email;
        $email_data[] = $email_obj;

        $member_email_template = $email_template->members_email;
        $member_email_array = [];
        if(!empty($member_email_template)){
            $member_email_array = explode(',', $member_email_template);
            $this->load->model('Birthday_Notifications_Model');
            $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
            if(!empty($members_data)){
                foreach ($members_data as $key => $val) {
                    if(empty($val->stf_email))
                        continue;

                    $email_obj = new stdClass();
                    $email_obj->stakeholder_id = $val->staff_id;
                    $email_obj->avatar_type = $val->avatar_type;
                    $email_obj->email = $val->stf_email;
                    $email_data[] = $email_obj;
                }
            }
        }
        $subject = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_template->email_subject);
        $replaceStaffName  = str_replace('%%staff_name%%', $staff_details->staff_name, $email_template->content);
        $body  = str_replace('%%financial_year%%', $financial_year_details->f_year, $replaceStaffName);
        $member_email = [];
        if(!empty($member_email_array)){
            foreach ($member_email_array as $email) {
                $member_email[] = trim($email);
            }
        }
        $member_email[] = $staff_details->to_email;

        $email_master_data = array(
            'subject' => $subject,
            'body' => $body,
            'source' => 'Manage Investment Declaration Apply New Regime',
            'sent_by' => $sent_by,
            'recievers' => "Staff",
            'from_email' => $email_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        $this->load->helper('email_helper');
        sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
    }

    public function delete_tds_staff_by_id(){
        $input = $this->input->post();
        if(!isset($input['staff_id']) || !isset($input['financial_year'])){
            return 0;
        }
        if(empty($input['staff_id']) || empty($input['financial_year']) || $input['staff_id'] <= 0 || $input['staff_id'] == '' || $input['financial_year'] <= 0 || $input['financial_year'] == ''){
            return 0;
        }
        $result = $this->payroll_model->delete_tds_staff_by_id($input);
        echo json_encode($result);
    }

    public function get_staffs_to_be_added_to_tds(){
        $input = $this->input->post();
        if(!isset($input['financial_year'])){
            return 0;
        }
        if(empty($input['financial_year']) || $input['financial_year'] <= 0 || $input['financial_year'] == ''){
            return 0;
        }
        $result = $this->payroll_model->get_staffs_to_be_added_to_tds($input);
        echo json_encode($result);
    }

    public function add_staffs_to_tds(){
        $input = $this->input->post();
        if(!isset($input['staff_ids']) || !isset($input['financial_year'])){
            return 0;
        }
        if(empty($input['staff_ids']) || empty($input['financial_year']) || $input['staff_ids'] <= 0 || $input['staff_ids'] == '' || $input['financial_year'] <= 0 || $input['financial_year'] == ''){
            return 0;
        }

        $result = $this->payroll_model->add_staffs_to_tds($input);
        echo json_encode($result);
    }

    public function get_approved_staffs_count(){
        $input = $this->input->post();
        $result = $this->payroll_model->get_approved_staffs_count($input);
        echo json_encode($result);
    }

    public function re_income_tax_staff_declartion(){
        $staff_id = $_POST['staffid'];
        $financial_year = $_POST['financial_year_id'];
        $result = $this->payroll_model->income_tax_staff_declartion_superAdmin($staff_id, $financial_year);
    }

    public function incrementsReport(){
        $data['staff_details'] = $this->payroll_model->get_staff_details();
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        if(empty($data['financial_year'])){
            $data['main_content'] = 'staff/payroll/payslip_error_page';
            $this->load->view('inc/template', $data);
            return;
        }
        $current_year = date('Y');
        $currentMonth = date('n');
        foreach ($data['financial_year'] as $key => $val) {
            if ($currentMonth <= 3) {
                if (date('Y', strtotime($val->from_date)) == ($current_year - 1)) {
                    $val->selected = 1;
                    break;
                }
            } else {
                if (date('Y', strtotime($val->from_date)) == $current_year) {
                    $val->selected = 1;
                    break;
                }
            }
        }
        $data['staff_details'] = $this->payroll_model->get_staff_details();
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        $data['title'] = 'Increments Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/increments_report';
        }
        $this->load->view('inc/template', $data);
    }

    public function getIncrementsCycleData(){
        $scheduleId = $_POST['scheduleId'];
        $result = $this->payroll_model->getIncrementsCycleData($scheduleId);
        echo json_encode($result);
    }

    public function getIncrementsData(){
        $incrementCycleId = $_POST['incrementCycleId'];
        $staffId = $_POST['staffId'];
        $scheduleId = $_POST['scheduleId'];
        $result = $this->payroll_model->getIncrementsData($staffId, $incrementCycleId, $scheduleId);
        echo json_encode($result);
    }
    
    public function staff_wise_payroll_report(){
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $currentMonth = date('n');
        foreach ($data['financial_year'] as $key => $val) {
            if ($currentMonth <= 3) {
                if (date('Y', strtotime($val->from_date)) == ($current_year - 1)) {
                    $val->selected = 1;
                    break;
                }
            } else {
                if (date('Y', strtotime($val->from_date)) == $current_year) {
                    $val->selected = 1;
                    break;
                }
            }
        }
        $data['staff_details'] = $this->payroll_model->get_staff_details();
        $data['staff_type'] = $this->settings->getSetting('staff_type');
        
        $data['main_content'] = 'management/payroll/staff_wise_payroll_report';
        $this->load->view('inc/template', $data);
    }

    public function payroll_edit_history_report(){
        // $data['staffDetails'] = $this->payroll_model->get_staff_details();
        // echo "<pre>";print_r($data['staffDetails']);die();
        $data['main_content'] = 'management/payroll/payroll_edit_history_report';
        $this->load->view('inc/template', $data);
    }

    public function  get_payroll_edit_history(){
        $result = $this->payroll_model->get_payroll_edit_history($_POST['from_date'],$_POST['to_date'],$_POST['staff_name']);
        echo json_encode($result);
    }

    public function get_staff_wise_payroll_data(){
        $staff_id = $this->input->post('staff_id');
        $financial_year = $this->input->post('financial_year');
        $result = $this->payroll_model->get_staff_wise_payroll_data($staff_id, $financial_year);
        $staffDeclarationStatus = $this->payroll_model->get_income_tax_delcaration_details_staff_wise($staff_id, $financial_year);
        $schedules = $this->payroll_model->staff_loan_amount_schedule($financial_year);
        $merged_data = [];
        foreach ($result as $id => $data) {
            foreach ($schedules as $schedule) {
                if ($schedule->id == $id) {
                    $data['schedule_name'] = $schedule->schedule_name;
                    $data['schedule_id'] = $schedule->id;
                    $merged_data[] = $data;
                    break;
                }
            }
        }
        echo json_encode(array('merged_data'=>$merged_data, 'schedules'=>$schedules, 'staffDeclarationStatus' => (!empty($staffDeclarationStatus) ? $staffDeclarationStatus->status : 'N/A' )));
    }

    public function default_approve_income_tax_assinged_staff_wise(){
        $staff_id = $_POST['staff_id'];
        $financial_year = 2; // 2024-25
        $tax_data = $this->payroll_model->get_income_tax_delcaration_details_staff_wise($staff_id, $financial_year);
        if($tax_data->tax_regime !=0){
            $result_data = $this->payroll_model->income_tax_staff_declartion_superAdmin($staff_id, $financial_year); 
            $saveIncomeTaxStatus = $this->payroll_model->save_incometax_declaration_mass_super_admin($result_data, $staff_id, $financial_year);
        }
        if($saveIncomeTaxStatus){
            $getIncomeTaxData = $this->payroll_model->getincome_declaration($staff_id, $financial_year);
        }
        if($tax_data->tax_regime == 1){ // New Regime
            $nr_tax_amt_field = $getIncomeTaxData['nr_tax_amt']; 
            $staffid = $staff_id; 
            $nr_taxable_salary_field = $getIncomeTaxData['nr_taxable_salary']; 
            $nr_perquisite_income_field = $getIncomeTaxData['perquisite_income']; 
            $nr_80d_field = $getIncomeTaxData['d_80']; 
            $nr_80c_field = $getIncomeTaxData['c_80']; 
            $nr_80ccd_field = $getIncomeTaxData['ccd_80']; 
            $nr_total_income_field = $getIncomeTaxData['total_income']; 
            $nr_basic_tax_field = $getIncomeTaxData['nr_basic_tax']; 
            $nr_cess_field = $getIncomeTaxData['nr_cess'];
            $selected_financial_year_id = $financial_year;
            $this->payroll_model->save_new_regim($nr_tax_amt_field, $staffid, $nr_taxable_salary_field, $nr_80d_field, $nr_80c_field, $nr_total_income_field, $nr_basic_tax_field, $nr_cess_field, $nr_80ccd_field, $selected_financial_year_id, $nr_perquisite_income_field);
        }else if($tax_data->tax_regime == 2){ // Old Regime
            $or_tax_amt_field = $getIncomeTaxData['or_tax_amt']; 
            $staffid = $staff_id; 
            $or_taxable_salary_field = $getIncomeTaxData['or_taxable_salary']; 
            $or_perquisite_income_field = $getIncomeTaxData['perquisite_income']; 
            $or_total_income_field = $getIncomeTaxData['total_income']; 
            $or_80c_field = $getIncomeTaxData['c_80']; 
            $or_80d_field = $getIncomeTaxData['d_80']; 
            $or_80ccd_field = $getIncomeTaxData['ccd_80']; 
            $or_basic_tax_field = $getIncomeTaxData['or_basic_tax']; 
            $or_cess_field = $getIncomeTaxData['or_cess'];
            $selected_financial_year_id = $financial_year;
            $this->payroll_model->old_regim_save($or_tax_amt_field, $staffid, $or_taxable_salary_field, $or_total_income_field, $or_80c_field, $or_80d_field, $or_cess_field, $or_80ccd_field, $selected_financial_year_id, $or_perquisite_income_field, $or_basic_tax_field);
        }
        echo $this->payroll_model->income_tax_staff_approve($staff_id, $financial_year);
    }

    public function get_all_staff_payslip_generated_data(){
        $result = $this->payroll_model->all_staff_payslip_generated_list();
        echo json_encode($result);
    }

    public function get_income_tax_delcration_staff_list(){
        $staff_id = $this->input->post('staff_id');
        $financial_year = $this->input->post('financial_year');
        $tax_data = $this->payroll_model->get_income_tax_delcaration_details_staff_wise($staff_id, $financial_year);
        if(empty($tax_data)){
            echo json_encode([]);
            return;
        }
        $result_data = $this->payroll_model->income_tax_staff_declartion_superAdmin($staff_id, $financial_year, 'Tax Summary Report');
        if(empty($result_data)){
            echo json_encode([]);
            return;
        }
        $recent_generated_tds = $this->payroll_model->get_recent_generated_tds($staff_id, $financial_year);
        $total_rent_paid = $this->payroll_model->get_total_rent_paid($staff_id, $financial_year);
        $result_data['total_monthly_tds'] = isset($tax_data->total_monthly_tds) ? $tax_data->total_monthly_tds : 0.00 ;
        $result_data['recent_monthly_tds'] = (!empty($recent_generated_tds)) ?  $recent_generated_tds->tds : 0;
        $result_data['total_rent_paid'] = (!empty($total_rent_paid)) ?  $total_rent_paid->rent_amount_cal : 0;
        $totalGeneratedTdsTillLatestMonth = $this->payroll_model->getTotalGeneratedTdsTilllatestMonth($staff_id, $financial_year);
        $result_data['totalGeneratedTdsTillLatestMonth'] = $totalGeneratedTdsTillLatestMonth;
        $result_data['selected_regime'] = 'Not Declared';
        if(!empty($tax_data) && $tax_data->tax_regime != 0){
            $result_data['selected_regime'] = ($tax_data->tax_regime == 2) ? 'Old Regime' : 'New Regime';
        }
        echo json_encode($result_data);
    }

    public function send_email_staff_wise(){
        $staff_id = $this->input->post('staff_id');
        $financial_year = $this->input->post('financial_year');

        if($staff_id == null || $staff_id == '' || $staff_id == 0 || $financial_year == null || $financial_year == '' || $financial_year == 0){
            echo 0;
            return;
        }
        $financial_year_details = $this->payroll_model->get_financial_year_details($financial_year);
        $staff_details = $this->payroll_model->get_staff_details_for_email($staff_id, $financial_year);
        $email_template = $this->payroll_model->get_email_template('payroll staff tds email');

        if (empty($email_template) || $staff_details->staff_status != 2 || $staff_details->tds_status == '' || $staff_details->tds_status != 'Approved' || $staff_details->selected_regime == 0) {
            echo 0;
            return;
        }

        $sent_by = $this->authorization->getAvatarStakeHolderId();
        $sent_by_details = $this->payroll_model->get_sent_by_staff_details($sent_by);
        $tax_declaration_details = $this->payroll_model->getincome_declaration($staff_id, $financial_year);
        $tds = '-';
        $tableHTML = '';
        if($staff_details->selected_regime == 1){
            $tableHTML = $this->construct_new_tax_table($tax_declaration_details);
            $tds = $tax_declaration_details['nr_tax_amt_remaining'];
        } else if($staff_details->selected_regime == 2) {
            $tableHTML = $this->construct_old_tax_table($tax_declaration_details);
            $tds = $tax_declaration_details['or_tax_amt_remaining'];
        }

        $result = $this->payroll_model->get_staff_wise_payroll_data($staff_id, $financial_year);
        $schedules = $this->payroll_model->staff_loan_amount_schedule($financial_year);

        $merged_data = [];
        foreach ($result as $id => $data) {
            foreach ($schedules as $schedule) {
                if ($schedule->id == $id) {
                    $data['schedule_name'] = $schedule->schedule_name;
                    $data['schedule_id'] = $schedule->id;
                    $merged_data[] = $data;
                    break;
                }
            }
        }
        $staff_details_table = $this->construct_staff_details_table($staff_details, $tds);
        $monthly_tds_table = $this->construct_monthly_tds_table($merged_data);
        
        $acad_year_id = $this->settings->getSetting('academic_year_id');
        $school_name = $this->settings->getSetting('school_name');

        $email_data = [];
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $staff_details->staff_id;
        $email_obj->avatar_type = $staff_details->avatar_type;
        $email_obj->email = $staff_details->to_email;
        $email_data[] = $email_obj;

        $member_email_template = $email_template->members_email;
        $member_email_array = [];
        if(!empty($member_email_template)){
            $member_email_array = explode(',', $member_email_template);
            $this->load->model('Birthday_Notifications_Model');
            $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
            if(!empty($members_data)){
                foreach ($members_data as $key => $val) {
                    if(empty($val->stf_email))
                        continue;
    
                    $email_obj = new stdClass();
                    $email_obj->stakeholder_id = $val->staff_id;
                    $email_obj->avatar_type = $val->avatar_type;
                    $email_obj->email = $val->stf_email;
                    $email_data[] = $email_obj;
                }
            }
        }

        $subject = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_template->email_subject);

        $body_start = '<p>Dear Sir/Madam,</p><br><p>We hope this email finds you well.</p><br><p>We would like to inform you about the Tax Calculation based on the regime you have selected for the Financial Year '.$financial_year_details->f_year.'.<br>The following calculations are also available in the School Element ERP application.<br>Below are the details:<br></p>';
        $sent_by_name = isset($sent_by_details->staff_name) ? $sent_by_details->staff_name : "Admin";
        $body_end = '<!--<p>This calculation has been done in accordance with the latest tax regulations and your declared investment details. The monthly TDS amount will be deducted from your salary accordingly.</p><br><p>If you have any questions regarding the calculation or need further assistance, please feel free to reach out to the HR/Finance team at [HR Email] or [Contact Number].</p><br><p>Thank you for your attention to this matter.</p>-->
        <p>Thank you,<br>'.$sent_by_name.',<br>'.$school_name.'.</p>';
        $body  = $body_start . $staff_details_table . '<br>' . $tableHTML . '<br>' . $monthly_tds_table . $body_end;
        $member_email = [];
        if(!empty($member_email_array)){
            foreach ($member_email_array as $email) {
                $member_email[] = trim($email);
            }
        }
        $member_email[] = $staff_details->to_email;
        
        $email_master_data = array(
            'subject' => $subject,
            'body' => $body,
            'source' => 'Manage Income Tax Declaration',
            'sent_by' => $sent_by,
            'recievers' => "Staff",
            'from_email' => $email_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );

        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        $this->load->helper('email_helper');
        $sent = sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
        if ($sent == 1) {
            echo 1;
            return;
        } else {
            echo 0;
            return;
        }
    }

    public function construct_staff_details_table($staff_details, $tds){
        $table = '<table border="1" style="width:100%; text-align:center; border-collapse:collapse;">';
        $table .= '<thead>';
        $table .= '<tr>';
        $table .= '<th colspan="4" style="text-align:center;">Staff Details</th>';
        $table .= '</tr>';
        $table .= '<thead>';
        $table .= '<tr>';
        $table .= '<th>Name</th>';
        $table .= '<th>Employee Code</th>';
        $table .= '<th>PAN</th>';
        $table .= '<th>TDS</th>';
        $table .= '</tr>';
        $table .= '</thead>';
        $table .= '<tbody>';
        $table .= '<tr>';
        $table .= '<td>'.$staff_details->staff_name.'</td>';
        $table .= '<td>'.$staff_details->employee_code.'</td>';
        $table .= '<td>'.$staff_details->pan_number.'</td>';
        $table .= '<td>'.$tds.'</td>';
        $table .= '</tr>';
        $table .= '</tbody>';
        $table .= '</table>';
        return $table;
    }

    private function construct_monthly_tds_table($merged_data){
        $table = '<table border="1" style="width:100%; text-align:center; border-collapse:collapse;">';

        $first_half = array_slice($merged_data, 0, 6);
        $second_half = array_slice($merged_data, 6, 6);
        $table .= '<thead>';
        $table .= '<tr>';
        $table .= '<th colspan="4" style="text-align:center;">Monthly TDS Amount</th>';
        $table .= '</tr>';
        $table .= '<thead>';
        $table .= '<tr>';
        $table .= '<th>Month</th>';
        $table .= '<th>TDS</th>';
        $table .= '<th>Month</th>';
        $table .= '<th>TDS</th>';
        $table .= '</tr>';
        $table .= '</thead>';

        $table .= '<tbody>';

        for ($i = 0; $i < 6; $i++) {
            $table .= '<tr>';
            
            if (isset($first_half[$i])) {
                $table .= '<td>' . $first_half[$i]['schedule_name'] . '</td>';
                $table .= '<td>' . $first_half[$i]['tds'] . '</td>';
            } else {
                $table .= '<td></td><td></td>';
            }
            
            if (isset($second_half[$i])) {
                $table .= '<td>' . $second_half[$i]['schedule_name'] . '</td>';
                $table .= '<td>' . $second_half[$i]['tds'] . '</td>';
            } else {
                $table .= '<td></td><td></td>';
            }

            $table .= '</tr>';
        }

        $table .= '</tbody>';
        
        $table .= '</table>';
        return $table;
    }

    private function construct_new_tax_table($tax_declaration_details) {
        // Construct the table
        $table = '
        <table border="1" id="taxCalculationNewRegime" style="border-collapse: collapse;">
            <thead>
                <tr>
                    <th colspan="4" style="text-align:center;">New Regime Calculation</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td width="55%"><strong>Income from Salary</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Salary</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Basic Salary</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_basic_salary" value="' . htmlspecialchars($tax_declaration_details['basic_salary']) . '" style="border:none;" readonly></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">House Rent Allowance</td>
                    <td width="15%"><input type="text" id="nr_hra" value="' . htmlspecialchars($tax_declaration_details['hra']) . '" style="border: none;" readonly></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Allowance</strong></td>
                    <td width="15%"><input type="text" id="nr_other_allowance" value="' . htmlspecialchars($tax_declaration_details['other_allowance']) . '" style="border: none;" readonly></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Additional Allowance</strong></td>
                    <td width="15%"><input type="text" id="nr_additional_allowance" value="' . htmlspecialchars($tax_declaration_details['outside_ctc_allowances']) . '" style="border: none;" readonly></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_hra_other_allowance" value="' . htmlspecialchars($tax_declaration_details['hra_other_allowance']) . '" style="border: none;" readonly></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Perquisite Income</strong></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_perquisite_income" value="' . htmlspecialchars($tax_declaration_details['perquisite_income']) . '" style="border:none;" readonly></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Less Exempted</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Standard deduction u/s 16</td>
                    <td width="15%"><input type="text" id="nr_sd" value="' . htmlspecialchars($tax_declaration_details['nr_sd']) . '" style="border:none;" readonly></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Taxable Income From Salary</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_income_from_salary_pt" value="' . htmlspecialchars($tax_declaration_details['taxable_income_from_salary_new']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Income from other Employer</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_other_employer_income" value="' . htmlspecialchars($tax_declaration_details['other_employer_income']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Gross Salary Income</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_gross_salary_income" value="' . htmlspecialchars($tax_declaration_details['gross_salary_new']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Taxable Income as per New Regime</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_taxable_salary" value="' . htmlspecialchars($tax_declaration_details['nr_taxable_salary']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Income Tax As Per New Regime</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_basic_tax" value="' . htmlspecialchars($tax_declaration_details['nr_basic_tax']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Tax Rebate</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_tax_rebate" value="' . htmlspecialchars($tax_declaration_details['nr_tax_rebate']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Net Income Tax</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_net_income_tax" value="' . htmlspecialchars($tax_declaration_details['nr_net_income_tax']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Surcharge</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_surcharge" value="' . htmlspecialchars($tax_declaration_details['nr_surcharge']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Income Tax Including Surcharge</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_net_income_tax_surcharge" value="' . htmlspecialchars($tax_declaration_details['nr_net_income_tax_surcharge']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Cess</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_cess" value="' . htmlspecialchars($tax_declaration_details['nr_cess']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Tax Including Cess (New Regime)</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><strong><input type="text" id="nr_yearly_tds"  value="' . htmlspecialchars($tax_declaration_details['nr_tax_amt']) . '" style="border:none;" readonly></strong></td>
                </tr>
                <tr>
                    <td width="55%"><strong>TDS Deducted from Other Employer</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="nr_other_employer_tds"  value="' . htmlspecialchars($tax_declaration_details['other_employer_tds']) . '" style="border:none;" readonly></td>
                </tr>
                <tr>
                    <td width="55%"><strong>TDS to be Deducted (New Regime)</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><strong><input type="text" id="nr_final_tds"  value="' . htmlspecialchars($tax_declaration_details['nr_tax_amt_remaining']) . '" style="border:none;" readonly></strong></td>
                </tr>
            </tbody>
        </table>';

        return $table;
    }

    private function construct_old_tax_table($tax_declaration_details){
        $table = '
        <table border="1" id="taxCalculationOldRegime" style="border-collapse: collapse;">
            <thead>
                <tr>
                    <th colspan="4" style="text-align:center;">Old Regime Calculation</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td width="55%"><strong>Income from Salary</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Salary</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Basic Salary</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_basic_salary" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['basic_salary']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">House Rent Allowance </td>
                    <td width="15%"><input type="text" id="or_hra" style="border: none;" readonly value="' . htmlspecialchars($tax_declaration_details['hra']) . '"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Allowance</strong> </td>
                    <td width="15%"><input type="text" id="or_other_allowance" style="border: none;" readonly value="' . htmlspecialchars($tax_declaration_details['other_allowance']) . '"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Additional Allowance</strong> </td>
                    <td width="15%"><input type="text" id="or_additional_allowance" style="border: none;" readonly value="' . htmlspecialchars($tax_declaration_details['outside_ctc_allowances']) . '"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_hra_other_allowance" style="border: none;" readonly value="' . htmlspecialchars($tax_declaration_details['hra_other_allowance']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Perquisite Income</strong></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_perquisite_income" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['perquisite_income']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Less Exempted</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">House Rent Allowance u/s 10(13A) </td>
                    <td width="15%"><input type="text" id="or_hra_exemption" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['hra_exemption']) . '"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>				
                <tr>
                    <td width="55%">Standard deduction u/s 16 </td>
                    <td width="15%"><input type="text" id="or_sd" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_sd']) . '"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_sd_hra" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['hra_exemption']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Income From Salary</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_income_from_salary" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['income_from_salary_old']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Deduction u/s 16: Professional Tax</strong></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_pt" style="border:none;" readonly  value="' . htmlspecialchars($tax_declaration_details['pt_paid']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Taxable Income From Salary</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_income_from_salary_pt" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['taxable_income_from_salary_old']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Income from other Employer</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_other_employer_income" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['other_employer_income']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Interest Paid On Home Loan</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_sec24" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['sec_24']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Gross Salary Income</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_gross_salary_income" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['gross_salary_old']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Less : Deduction Under Chapter VI A</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80C</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80c" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['c_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80CCD</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80ccd" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['ccd_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80D</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80d" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['d_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80DD</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80dd" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['dd_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80DDB</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80ddb" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['ddb_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80G</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80g" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['g_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80E</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80e" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['e_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80U</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80u" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['u_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 80TTA/B</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_80ttab" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['ttab_80']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Less : Deduction Under Chapter III</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Deduction Under Section 10(5) - LTA / LTC</td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_lta" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_lta']) . '"></td>
                    <td width="15%"></td>
                </tr>
                <tr>
                    <td width="55%">Total Deductions</td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="total_80_deductions" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['total_80_deductions']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Taxable Income as per Old Regime</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_taxable_salary" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_taxable_salary']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Income Tax As Per Old Regime</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_basic_tax" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_basic_tax']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Tax Rebate</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_tax_rebate" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_tax_rebate']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Net Income Tax</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_net_income_tax" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_net_income_tax']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Surcharge</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_surcharge" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_surcharge']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Income Tax Including Surcharge</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_net_income_tax_surcharge" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_net_income_tax_surcharge']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Cess</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_cess" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_cess']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>Tax Including Cess (Old Regime)</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><strong><input type="text" id="or_yearly_tds" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_tax_amt']) . '"></strong></td>
                </tr>
                <tr>
                    <td width="55%">TDS Deducted from Other Employer</td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><input type="text" id="or_other_employer_tds" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['other_employer_tds']) . '"></td>
                </tr>
                <tr>
                    <td width="55%"><strong>TDS to be Deducted (Old Regime)</strong></td>
                    <td width="15%"></td>
                    <td width="15%"></td>
                    <td width="15%"><strong><input type="text" id="or_final_tds" style="border:none;" readonly value="' . htmlspecialchars($tax_declaration_details['or_tax_amt_remaining']) . '"></strong></td>
                </tr>
            </tbody>
        </table>';

        return $table;
    }

    public function download_payslip_as_pdf($staff_id = '', $schedule_id = '') {
        if ($staff_id == '') {
            $staff_id = $this->input->post('staff_id');
        }
        
        if ($schedule_id == '') {
            $schedule_id = $this->input->post('schedule_id');
        }
        $file_name_details = $this->payroll_model->get_staff_name_and_payslip_month($staff_id, $schedule_id);
        $file_name = $file_name_details['staff_name'] . ' ' . $file_name_details['schedule_name'] . ' payslip.pdf';
        $path = $this->Staff_Payroll_Model->get_payslip_pdf_path($staff_id, $schedule_id);
        if (empty($path)) {
            echo json_encode(['status' => 'error', 'message' => 'Payslip not found.']);
            return;
        }
        $url = $this->filemanager->getFilePath($path);
        // $url = 'https://s3.us-west-1.wasabisys.com/nextelement/skalvi/payslips/6757cf3ecb5c5-1733807934.pdf';
        // $data = file_get_contents($url);
        // $this->load->helper('download');
        // force_download($file_name, $data, TRUE);
        echo json_encode([
                'status' => 'success',
                'url' => $url,
                'file_name' => $file_name
            ]);
    }

    private function get_remote_file_size($url) {
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_NOBODY, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_MAXREDIRS, 3);

        $data = curl_exec($curl);
        curl_close($curl);

        if ($data) {
            if (preg_match('/Content-Length: (\d+)/', $data, $matches)) {
                return (int)$matches[1];
            }
        }
        return false;
    }

    public function download_all_staff_payslip(){
        $schedule_id = $this->input->post('schedule_id');
        $payslip_data = $this->payroll_model->get_payroll_data_for_regenerate_staff($schedule_id, 'all');
        $url_paths_arr = [];
        if(!empty($payslip_data)){
            foreach($payslip_data as $payslip){
                $url_paths= new stdClass();
                $path = $this->Staff_Payroll_Model->get_payslip_pdf_path($payslip->staff_id, $schedule_id);
                $url = $this->filemanager->getFilePath($path);
                $url_paths->url= $url;
                $url_paths->filename= $payslip->staff_name.' payslip.pdf';
                $url_paths->staff_id= $payslip->staff_id;
                $url_paths->schedule_id= $schedule_id;
                $url_paths_arr[] = $url_paths;
            }
            // $url_paths_arr = array_slice($url_paths_arr, 0, 2);
        }
        echo json_encode($url_paths_arr);
    }

    public function _calculate_state_wise_pt($state, $age_pt_cal, $monthly_gross, $staff_id){
        $staff_gender = $this->payroll_model->get_staff_gender($staff_id);

        if($staff_gender == '' || $staff_gender == null || !isset($staff_gender)){
            $staff_gender = 'M';
        }

        $cur_state = 'Karnataka';
        if(!empty($state)){
            $cur_state = $state->state;
        }

        if($cur_state == '' || $cur_state == null){
            $cur_state = 'Karnataka';
        }
        $pt = 0;
        switch ($cur_state) {
            case 'Karnataka':
                if ($monthly_gross >= 25000 && $age_pt_cal == 1) {
                    $pt = 200;
                }
                break;
            
            case 'Maharashtra':
                if ($staff_gender == 'M') {
                    if ($monthly_gross <= 7500) {
                        $pt = 0;
                    } elseif ($monthly_gross >= 7501 && $monthly_gross <= 10000) {
                        $pt = 175;
                    } elseif ($monthly_gross > 10000) {
                        $pt = 200;
                        if (date('F') == 'February') {
                            $pt = 300;
                        }
                    }
                } elseif ($staff_gender == 'F') {
                    if ($monthly_gross <= 25000) {
                        $pt = 0;
                    } elseif ($monthly_gross > 25000) {
                        $pt = 200;
                        if (date('F') == 'February') {
                            $pt = 300;
                        }
                    }
                }
                break;
            
            case 'Telangana':
                if ($monthly_gross <= 15000) {
                    $pt = 0;
                } elseif ($monthly_gross > 15000 && $monthly_gross <= 20000) {
                    $pt = 150;
                } else {
                    $pt = 200;
                }
                break;
            
            case 'Andhra Pradesh':
                if ($monthly_gross <= 15000) {
                    $pt = 0;
                } elseif ($monthly_gross > 15000 && $monthly_gross <= 20000) {
                    $pt = 150;
                } elseif ($monthly_gross > 20000) {
                    $pt = 200;
                }
                break;
            
            // case 'Tamil Nadu':
            //     if ($monthly_gross <= 21000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 21000 && $monthly_gross <= 30000) {
            //         $pt = 100;
            //     } elseif ($monthly_gross > 30000 && $monthly_gross <= 45000) {
            //         $pt = 235;
            //     } elseif ($monthly_gross > 45000 && $monthly_gross <= 60000) {
            //         $pt = 510;
            //     } elseif ($monthly_gross > 60000 && $monthly_gross <= 75000) {
            //         $pt = 760;
            //     } elseif ($monthly_gross > 75000) {
            //         $pt = 1095;
            //     }
            //     break;

            // case 'Assam':
            //     if ($monthly_gross <= 10000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 10000 && $monthly_gross <= 15000) {
            //         $pt = 150;
            //     } elseif ($monthly_gross > 15000 && $monthly_gross <= 25000) {
            //         $pt = 180;
            //     } elseif ($monthly_gross > 25000) {
            //         $pt = 208;
            //     }
            //     break;

            // case 'Bihar':
            //     if ($monthly_gross <= 300000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 300000 && $monthly_gross <= 500000) {
            //         $pt = 1000;
            //     } elseif ($monthly_gross > 500000 && $monthly_gross <= 1000000) {
            //         $pt = 2000;
            //     } elseif ($monthly_gross > 1000000) {
            //         $pt = 2500;
            //     }
            //     break;

            // case 'Gujarat':
            //     if ($monthly_gross <= 12000) {
            //         $pt = 0;
            //     } else {
            //         $pt = 200;
            //     }
            //     break;

            // case 'Jharkhand':
            //     if ($monthly_gross <= 300000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 300000 && $monthly_gross <= 500000) {
            //         $pt = 1200;
            //     } elseif ($monthly_gross > 500000 && $monthly_gross <= 800000) {
            //         $pt = 1800;
            //     } elseif ($monthly_gross > 800000 && $monthly_gross <= 1000000) {
            //         $pt = 2100;
            //     } elseif ($monthly_gross > 1000000) {
            //         $pt = 2500;
            //     }
            //     break;

            // case 'Kerala':
            //     if ($monthly_gross <= 11999) {
            //         $pt = 0;
            //     } elseif ($monthly_gross >= 12000 && $monthly_gross <= 17999) {
            //         $pt = 320;
            //     } elseif ($monthly_gross >= 18000 && $monthly_gross <= 29999) {
            //         $pt = 450;
            //     } elseif ($monthly_gross >= 30000 && $monthly_gross <= 44999) {
            //         $pt = 600;
            //     } elseif ($monthly_gross >= 45000 && $monthly_gross <= 99999) {
            //         $pt = 750;
            //     } elseif ($monthly_gross >= 100000 && $monthly_gross <= 124999) {
            //         $pt = 1000;
            //     } elseif ($monthly_gross >= 125000 && $monthly_gross <= 2000000) {
            //         $pt = 1250;
            //     }
            //     break;

            // case 'Madhya Pradesh':
            //     if ($monthly_gross <= 225000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 225000 && $monthly_gross <= 300000) {
            //         $pt = 1500;
            //     } elseif ($monthly_gross > 300000 && $monthly_gross <= 400000) {
            //         $pt = 2000;
            //     } elseif ($monthly_gross > 400000) {
            //         $pt = 2500;
            //     }
            //     break;

            // case 'Manipur':
            //     if ($monthly_gross <= 50000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 50000 && $monthly_gross <= 75000) {
            //         $pt = 1200;
            //     } elseif ($monthly_gross > 75000 && $monthly_gross <= 100000) {
            //         $pt = 2000;
            //     } elseif ($monthly_gross > 100000 && $monthly_gross <= 125000) {
            //         $pt = 2400;
            //     } else {
            //         $pt = 2500;
            //     }
            //     break;

            // case 'Meghalaya':
            //     if ($monthly_gross <= 50000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 50000 && $monthly_gross <= 75000) {
            //         $pt = 200;
            //     } elseif ($monthly_gross > 75000 && $monthly_gross <= 100000) {
            //         $pt = 300;
            //     } elseif ($monthly_gross > 100000 && $monthly_gross <= 150000) {
            //         $pt = 500;
            //     } elseif ($monthly_gross > 150000 && $monthly_gross <= 200000) {
            //         $pt = 750;
            //     } elseif ($monthly_gross > 200000 && $monthly_gross <= 250000) {
            //         $pt = 1000;
            //     } elseif ($monthly_gross > 250000 && $monthly_gross <= 300000) {
            //         $pt = 1250;
            //     } elseif ($monthly_gross > 300000 && $monthly_gross <= 350000) {
            //         $pt = 1500;
            //     } elseif ($monthly_gross > 350000 && $monthly_gross <= 400000) {
            //         $pt = 1800;
            //     } elseif ($monthly_gross > 400000 && $monthly_gross <= 450000) {
            //         $pt = 2100;
            //     } elseif ($monthly_gross > 450000 && $monthly_gross <= 500000) {
            //         $pt = 2400;
            //     } else {
            //         $pt = 2500;
            //     }
            //     break;

            // case 'Mizoram':
            //     if ($monthly_gross <= 5000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 5000 && $monthly_gross <= 8000) {
            //         $pt = 75;
            //     } elseif ($monthly_gross > 8000 && $monthly_gross <= 10000) {
            //         $pt = 120;
            //     } elseif ($monthly_gross > 10000 && $monthly_gross <= 12000) {
            //         $pt = 150;
            //     } elseif ($monthly_gross > 12000 && $monthly_gross <= 15000) {
            //         $pt = 180;
            //     } elseif ($monthly_gross > 15000 && $monthly_gross <= 20000) {
            //         $pt = 195;
            //     } else {
            //         $pt = 208;
            //     }
            //     break;
            
            // case 'Nagaland':
            //     if ($monthly_gross <= 4000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 4000 && $monthly_gross <= 5000) {
            //         $pt = 35;
            //     } elseif ($monthly_gross > 5000 && $monthly_gross <= 7000) {
            //         $pt = 75;
            //     } elseif ($monthly_gross > 7000 && $monthly_gross <= 9000) {
            //         $pt = 110;
            //     } elseif ($monthly_gross > 9000 && $monthly_gross <= 12000) {
            //         $pt = 180;
            //     } else {
            //         $pt = 208;
            //     }
            //     break;

            // case 'Odisha':
            //     if ($monthly_gross <= 160000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 160000 && $monthly_gross <= 300000) {
            //         $pt = 1500; // Monthly Rs. 125
            //     } else {
            //         $pt = 2400; // Monthly Rs. 200/- (Apr to Feb)
            //     }
            //     break;

            // case 'Puducherry':
            //     if ($monthly_gross <= 99999) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 99999 && $monthly_gross <= 200000) {
            //         $pt = 250;
            //     } elseif ($monthly_gross > 200000 && $monthly_gross <= 300000) {
            //         $pt = 500;
            //     } elseif ($monthly_gross > 300000 && $monthly_gross <= 400000) {
            //         $pt = 750;
            //     } elseif ($monthly_gross > 400000 && $monthly_gross <= 500000) {
            //         $pt = 1000;
            //     } else {
            //         $pt = 1250;
            //     }
            //     break;

            // case 'Punjab':
            //     if ($monthly_gross > 250000) {
            //         $pt = 200;
            //     }
            //     break;

            // case 'Sikkim':
            //     if ($monthly_gross <= 20000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 20000 && $monthly_gross <= 30000) {
            //         $pt = 125;
            //     } elseif ($monthly_gross > 30000 && $monthly_gross <= 40000) {
            //         $pt = 150;
            //     } else {
            //         $pt = 200;
            //     }
            //     break;

            // case 'Tripura':
            //     if ($monthly_gross <= 7500) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 7500 && $monthly_gross <= 15000) {
            //         $pt = 1800; // Monthly Rs. 150
            //     } else {
            //         $pt = 2496; // Monthly Rs. 208
            //     }
            //     break;

            // case 'West Bengal':
            //     if ($monthly_gross <= 8500) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 8500 && $monthly_gross <= 10000) {
            //         $pt = 0;
            //     } elseif ($monthly_gross > 10000 && $monthly_gross <= 15000) {
            //         $pt = 110;
            //     } elseif ($monthly_gross > 15000 && $monthly_gross <= 25000) {
            //         $pt = 130;
            //     } elseif ($monthly_gross > 25000 && $monthly_gross <= 40000) {
            //         $pt = 150;
            //     } else {
            //         $pt = 200;
            //     }
            //     break;

            default:
                if ($monthly_gross >= 25000  && $age_pt_cal == 1) {
                    $pt = 200;
                }
                break;
        }

        return $pt;

    }

    private function _calculate_payroll_salary_mannualy($result) {
        $monthly_basic_salary = $result->monthly_basic_salary ? round($result->monthly_basic_salary,0) : '';
        $monthly_gross = round($result->monthly_gross,0);
        $staff_da = $result->staff_da ? round($result->staff_da,0) : '';
        $staff_hra = $result->staff_hra ? round($result->staff_hra,0) : '';
        $pf_employee_contribution = $result->pf_employee_contribution ? round($result->pf_employee_contribution,0) : '';
        $pf_for_employer = $result->pf_for_employer_salary ? round($result->pf_for_employer_salary,0) : '';
        $professional_tax = $result->professional_tax ? round($result->professional_tax,0) : '';
        $esi_employee_contribution = $result->esi_employee_contribution ? round($result->esi_employee_contribution,0) : '';

        $yearlyCTC = round($monthly_gross * 12,0);

        $yearlyGross = round($monthly_basic_salary * 12, 0);

        $columns = $this->payroll_model->get_payroll_column_table();
        $earnings = [];
        $deduction = [];
        foreach ($columns->payroll_column as $key => $val) {
            if($val->type == 'earnings'){
                array_push($earnings, $val);
            }
            if($val->type == 'deduction'){
                array_push($deduction, $val);
            }
        }

        // Return calculated values
        $result =  array(
            'monthly_gross' => $monthly_gross,
            'monthly_basic_salary' => $monthly_basic_salary,
            'staff_hra' => $staff_hra,
            'yearly_gross' => $yearlyGross,
            'yearly_ctc' => $yearlyCTC,
            'staff_da' => $staff_da,
            'medical_allowance' => '0',
            'cca' => '0',
            'staffta' => '0',
            'professional_tax' => $professional_tax,
            'pf_employee_contribution' => $pf_employee_contribution,
            'pf_for_employer' => $pf_for_employer,
            'lta' => '0',
            'special_allowance' => '0',
            'conveyance'=>'0',
            'transport_allowance'=>'0',
            'extra_allowance'=>'0',
            'hra_fixed'=>'0',
            'co_ordinator_allowance'=>'0',
            'ib_retention_allowance'=>'0',
            'house_master_allowance'=>'0',
            'vpf'=>'0',
            'esi'=>$esi_employee_contribution
        );
        $result['earnings'] = 0;
        $result['deducation'] = 0;
        foreach ($earnings as $obj) {
            if ($obj->total_earings_include == 1) {
                $key = $obj->column_name;
                if (isset($result[$key])) {
                    $result['earnings'] += floatval($result[$key]);
                }
            }
        }
        foreach ($deduction as $obj) {
            if ($obj->total_deduction_include == 1) {
                $key = $obj->column_name;
                if (isset($result[$key])) {
                    $result['deducation'] += floatval($result[$key]);
                }
            }
        }
        return $result;
    }

    public function getAdditionalAllowanceBreakDown(){
        $input = $this->input->post();
        $result = $this->payroll_model->getAdditionalAllowanceBreakDown($input);
        echo json_encode($result);
    }

    public function incomeTaxReopenProofSubmission(){
        $staffId = $_POST['staffId'];
        $financialYear = $_POST['financialYear'];
        $selectedRegime = 0;
        if(isset($_POST['selectedRegimeForProofSubmission'])){
            $selectedRegime = $_POST['selectedRegimeForProofSubmission'];
        }
        $result = $this->payroll_model->incomeTaxReopenProofSubmission($staffId, $financialYear, $selectedRegime);
        $financial_year_details = $this->payroll_model->get_financial_year_details($financialYear);
        $staff_details = $this->payroll_model->get_staff_details_for_email($staffId, $financialYear);
        $email_template = $this->payroll_model->get_email_template('payroll staff tds reopen for proof submission');
        if (!empty($email_template)) {
            $sent_by = $this->authorization->getAvatarStakeHolderId();
            $sent_by_details = $this->payroll_model->get_sent_by_staff_details($sent_by);

            $acad_year_id = $this->settings->getSetting('academic_year_id');
            $school_name = $this->settings->getSetting('school_name');

            $email_data = [];
            $email_obj = new stdClass();
            $email_obj->stakeholder_id = $staff_details->staff_id;
            $email_obj->avatar_type = $staff_details->avatar_type;
            $email_obj->email = $staff_details->to_email;
            $email_data[] = $email_obj;
            $member_email_template = $email_template->members_email;
            $member_email_array = [];
            if(!empty($member_email_template)){
                $member_email_array = explode(',', $member_email_template);
                $this->load->model('Birthday_Notifications_Model');
                $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
                if(!empty($members_data)){
                    foreach ($members_data as $key => $val) {
                        if(empty($val->stf_email))
                            continue;
        
                        $email_obj = new stdClass();
                        $email_obj->stakeholder_id = $val->staff_id;
                        $email_obj->avatar_type = $val->avatar_type;
                        $email_obj->email = $val->stf_email;
                        $email_data[] = $email_obj;
                    }
                }
            }
            $subject  = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_template->email_subject);
            $body1  = str_replace('%%staff_name%%', $staff_details->staff_name, $email_template->content);
            $body  = str_replace('%%financial_year%%', $financial_year_details->f_year, $body1);
            $member_email = [];
            if(!empty($member_email_array)){
                foreach ($member_email_array as $email) {
                    $member_email[] = trim($email);
                }
            }
            $member_email[] = $staff_details->to_email;
            $email_master_data = array(
                'subject' => $subject,
                'body' => $body,
                'source' => 'Staff Tax Declaration Reopen For Proof Submission',
                'sent_by' => $sent_by,
                'recievers' => "Staff",
                'from_email' => $email_template->registered_email,
                'files' => NULL,
                'acad_year_id' => $acad_year_id,
                'visible' => 1,
                'sender_list' => NULL,
                'sending_status' => 'Completed'
            );
            $this->load->model('communication/emails_model');
            $email_master_id = $this->emails_model->saveEmail($email_master_data);
            $this->emails_model->save_sending_email_data($email_data, $email_master_id);
            $this->load->helper('email_helper');
            $sent = sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
        }
        echo json_encode($result);
    }

    public function viewProofAttachments(){
        $staffId = $_POST['staffId'];
        $financialYear = $_POST['financialYear'];
        $result = $this->payroll_model->viewProofAttachments($staffId, $financialYear);
        if(!empty($result) && !isset($result[0]->proof_submission_status)){
            foreach($result as $file){
                $file->proof_file_url = $this->filemanager->getFilePath($file->proof_file_url);
            }
        }
        echo json_encode($result);
    }

    public function sendApproveRejectRequest(){
        $input = $this->input->post();
        $staffId = $input['staffId'];
        $financialYear = $input['financialYear'];
        $rowID = $input['rowID'];
        $status = $input['status'];
        $result = $this->payroll_model->sendApproveRejectRequest($staffId, $financialYear, $rowID, $status);
        if($result){
            $financial_year_details = $this->payroll_model->get_financial_year_details($financialYear);
            $staff_details = $this->payroll_model->get_staff_details_for_email($staffId, $financialYear);
            if($status == 'Rejected'){
                $email_template = $this->payroll_model->get_email_template('payroll staff no investment reject');
                if (!empty($email_template)) {
                    $this->staffProofSubmissionEmail($staffId, $financialYear, $financial_year_details, $staff_details, $email_template, 'No Investment Rejected');
                }
            }

            if($status == 'Approved'){
                $email_template = $this->payroll_model->get_email_template('payroll staff no investment approve');
                if (!empty($email_template)) {
                    $this->staffProofSubmissionEmail($staffId, $financialYear, $financial_year_details, $staff_details, $email_template, 'No Investment Approved');
                }
            }
        }
        echo json_encode($result);
    }

    public function approveRejectStaffProofAttachment(){
        $input = $this->input->post();
        $staffId = $input['staffId'];
        $financialYear = $input['financialYear'];
        $attachmentId = $input['attachmentId'];
        $status = $input['status'];
        $columnName = $input['columnName'];
        $remarks = $input['remarks'];
        $result = $this->payroll_model->approveRejectStaffProofAttachment($staffId, $financialYear, $attachmentId, $status, $remarks);
        // echo "<pre>";print_r($result);die();
        if($result['approveRejectedStatus']){
            $financial_year_details = $this->payroll_model->get_financial_year_details($financialYear);
            $staff_details = $this->payroll_model->get_staff_details_for_email($staffId, $financialYear);

            if ($result['reopenStatus'] == 1) {
                $email_template = $this->payroll_model->get_email_template('payroll staff resubmit investment proof');
                if (!empty($email_template)) {
                    $this->staffProofSubmissionEmail($staffId, $financialYear, $financial_year_details, $staff_details, $email_template, 'Resubmit');
                }
            }

            if($result['approvedStatus'] == 1){
                $email_template = $this->payroll_model->get_email_template('payroll staff approved investment proof');
                if (!empty($email_template)) {
                    $this->staffProofSubmissionEmail($staffId, $financialYear, $financial_year_details, $staff_details, $email_template, 'Approved');
                }
            }
        }
        echo json_encode(array($result));
    }

    private function staffProofSubmissionEmail($staffId, $financialYear, $financial_year_details, $staff_details, $email_template, $type){
        $sent_by = $this->authorization->getAvatarStakeHolderId();
        $sent_by_details = $this->payroll_model->get_sent_by_staff_details($sent_by);
        
        $acad_year_id = $this->settings->getSetting('academic_year_id');
        $school_name = $this->settings->getSetting('school_name');
        
        $email_data = [];
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $staff_details->staff_id;
        $email_obj->avatar_type = $staff_details->avatar_type;
        $email_obj->email = $staff_details->to_email;
        $email_data[] = $email_obj;
        $member_email_template = $email_template->members_email;
        $member_email_array = [];
        if(!empty($member_email_template)){
            $member_email_array = explode(',', $member_email_template);
            $this->load->model('Birthday_Notifications_Model');
            $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
            if(!empty($members_data)){
                foreach ($members_data as $key => $val) {
                    if(empty($val->stf_email))
                        continue;
    
                    $email_obj = new stdClass();
                    $email_obj->stakeholder_id = $val->staff_id;
                    $email_obj->avatar_type = $val->avatar_type;
                    $email_obj->email = $val->stf_email;
                    $email_data[] = $email_obj;
                }
            }
        }
        $subject = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_template->email_subject);
        $replaceStaffName  = str_replace('%%staff_name%%', $staff_details->staff_name, $email_template->content);
        $body  = str_replace('%%financial_year%%', $financial_year_details->f_year, $replaceStaffName);
        $member_email = [];
        if(!empty($member_email_array)){
            foreach ($member_email_array as $email) {
                $member_email[] = trim($email);
            }
        }
        $member_email[] = $staff_details->to_email;
        $source = '';
        if($type == 'Approved'){
            $source = 'Staff Tax Declaration Proofs Approved';
        } else if($type == 'Resubmit') {
            $source = 'Staff Tax Declaration Reopen For Re-Submitting Proofs';
        } else if($type == 'No Investment Rejected'){
            $source = 'Staff No Investment Tax Declaration Rejected';
        } else if($type == 'No Investment Approved'){
            $source = 'Staff No Investment Tax Declaration Approved';
        }
        $email_master_data = array(
            'subject' => $subject,
            'body' => $body,
            'source' => $source,
            'sent_by' => $sent_by,
            'recievers' => "Staff",
            'from_email' => $email_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        $this->load->helper('email_helper');
        $sent = sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
    }

    public function getStaffEditHistoryPayroll(){
        $staffId = $this->input->post('staffId');
        $financialYear = $this->input->post('financialYear');
        $result = $this->payroll_model->getStaffEditHistoryPayroll($staffId, $financialYear);
        echo json_encode($result);
    }

    public function sendTaxProofSubmissionRemainderEmailToStaff(){
        $staff_id = $this->input->post('staff_id');
        $financial_year = $this->input->post('financial_year');

        if($staff_id == null || $staff_id == '' || $staff_id == 0 || $financial_year == null || $financial_year == '' || $financial_year == 0){
            echo 0;
            return;
        }

        $financial_year_details = $this->payroll_model->get_financial_year_details($financial_year);
        $staff_details = $this->payroll_model->get_staff_details_for_email($staff_id, $financial_year);
        $email_template = $this->payroll_model->get_email_template('payroll tax proof submission remainder');

        if (empty($email_template) || $staff_details->staff_status != 2 || $staff_details->tds_status == '' || $staff_details->tds_status != 'Approved' || $staff_details->selected_regime == 0) {
            echo 0;
            return;
        }

        $sent_by = $this->authorization->getAvatarStakeHolderId();
        
        $acad_year_id = $this->settings->getSetting('academic_year_id');
        $school_name = $this->settings->getSetting('school_name');

        $email_data = [];
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $staff_details->staff_id;
        $email_obj->avatar_type = $staff_details->avatar_type;
        $email_obj->email = $staff_details->to_email;
        $email_data[] = $email_obj;

        $member_email_template = $email_template->members_email;
        $member_email_array = [];
        if(!empty($member_email_template)){
            $member_email_array = explode(',', $member_email_template);
            $this->load->model('Birthday_Notifications_Model');
            $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
            if(!empty($members_data)){
                foreach ($members_data as $key => $val) {
                    if(empty($val->stf_email))
                        continue;
    
                    $email_obj = new stdClass();
                    $email_obj->stakeholder_id = $val->staff_id;
                    $email_obj->avatar_type = $val->avatar_type;
                    $email_obj->email = $val->stf_email;
                    $email_data[] = $email_obj;
                }
            }
        }
        
        $subject = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_template->email_subject);
        $replaceFinancialYear  = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_template->content);
        $body  = str_replace('%%staff_name%%', $staff_details->staff_name, $replaceFinancialYear);
        $member_email = [];
        if(!empty($member_email_array)){
            foreach ($member_email_array as $email) {
                $member_email[] = trim($email);
            }
        }
        $member_email[] = $staff_details->to_email;
        
        $email_master_data = array(
            'subject' => $subject,
            'body' => $body,
            'source' => 'Manage Income Tax Declaration',
            'sent_by' => $sent_by,
            'recievers' => "Staff",
            'from_email' => $email_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );

        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        $this->load->helper('email_helper');
        $sent = sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
        if ($sent == 1) {
            echo 1;
            return;
        } else {
            echo 0;
            return;
        }
    }

    public function monthwiseTdsDeducteeReport(){
        if (!$this->authorization->isAuthorized('PAYROLL.MONTHWISE_TDS_DEDUCTEE_REPORT')) {
            redirect('dashboard', 'refresh');
        }
        $data['financial_year'] = $this->payroll_model->get_financial_year();
        $current_year = date('Y');
        $current_month = date('m');
        $selected_financial_year = null;
        foreach ($data['financial_year'] as $financial_year) {
            $f_year_split = explode('-', $financial_year->f_year);
            $start_year = trim($f_year_split[0]);
            $end_year = trim($f_year_split[1]);
            if ($end_year < 100) {
                $end_year = (int) (floor($start_year / 100) * 100) + $end_year;
            }
            if ($current_year == $start_year && $current_month >= 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
            elseif ($current_year == $end_year && $current_month < 4) {
                $selected_financial_year = $financial_year->id;
                break;
            }
        }
        if ($selected_financial_year == null && !empty($data['financial_year'])) {
            $selected_financial_year = $data['financial_year'][0]->id;
        }
        $data['selected_financial_year'] = $selected_financial_year;
        $data['title'] = 'Month-wise TDS Deductee Report';
        if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
            $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
        }else{
            $data['main_content'] = 'management/payroll/month_wise_tds_deductee_report';
        }
        $this->load->view('inc/template', $data);
    }

    public function getStaffWisePayslipData(){
        $input = $this->input->post();
        // echo "<pre>";print_r($input);die();
        $schedule = $this->input->post('schedule');
        $financialYear = $this->input->post('financialYear');
        $staffData = json_decode($this->input->post('staffData'), true);
        
        if (empty($schedule) || empty($staffData)) {
            echo json_encode([]);
            return;
        }
        
        $result = [];

        $staff_ids = array_column($staffData, 'staff_id');
        $schedule_ids = is_array($schedule) ? $schedule : explode(',', $schedule);

        // Fetch all required TDS & payment data in a single query
        $tdsData = $this->payroll_model->getTdsAndDisbursement($staff_ids, $schedule_ids);

        // Map data for quick access
        $tdsMap = [];
        foreach ($tdsData as $data) {
            $tdsMap[$data->staff_id] = [
                'tds' => $data->tds,
                'payment_date' => $data->payment_date,
                'total_earnings' => $data->total_earnings
            ];
        }

        foreach ($staffData as $staff) {
            $staff_id = $staff['staff_id'];
            $tdsData = isset($tdsMap[$staff_id]) ? $tdsMap[$staff_id] : null;
            if ($tdsData == null || $tdsData['payment_date'] == null) {
                continue; // Skip adding this staff to the result
            }
            // $staff['tds'] = isset($tdsMap[$staff_id]) ? $tdsMap[$staff_id]['tds'] : null;
            // $staff['payment_date'] = isset($tdsMap[$staff_id]) ? $tdsMap[$staff_id]['payment_date'] : null;
            // $staff['total_earnings'] = isset($tdsMap[$staff_id]) ? $tdsMap[$staff_id]['total_earnings'] : null;
            $staff = array_merge($staff, $tdsData);
            $result[] = $staff;
        }
        echo json_encode($result);
    }

    // public function standardIncomeTaxReport(){
    //     $data['financial_year'] = $this->payroll_model->get_financial_year();
    //     $currentYear = date('Y');
    //     $currentMonth = date('n');
    //     foreach ($data['financial_year'] as $key => $val) {
    //         if ($currentMonth <= 3) {
    //             if (date('Y', strtotime($val->from_date)) == ($currentYear - 1)) {
    //                 $schedule_year = $val->id;
    //                 break;
    //             }
    //         } else {
    //             if (date('Y', strtotime($val->from_date)) == $currentYear) {
    //                 $schedule_year = $val->id;
    //                 break;
    //             }
    //         }
    //     }
    //     $data['schedule_year'] = $schedule_year;
    //     //$data['sStatus'] = $this->settings->getSetting('staff_status');
    //     $data['main_content'] = 'management/payroll/standard_income_tax_report';
    //     $this->load->view('inc/template', $data);
    // }

    // public function getIncomeTaxData(){
    //     $financialYear = $_POST['financialYear'];
    //     // $result = $this->payroll_model->get_income_declaration_details(2, '');
    //     $staffTaxDetails = $this->payroll_model->get_income_declaration_details(2, '');
    //     foreach ($staffTaxDetails as $key => $value) {
    //         $taxDetails = $this->payroll_model->getincome_declaration($value->staff_id, 2);
    //         $additionalStaffDetails = $this->payroll_model->all_staff_payslip_generated_list($value->staff_id);
    //         // echo "<pre>";print_r($additionalStaffDetails);die();
    //         $value->taxDetails = $taxDetails;
    //         $value->additionalStaffDetails = $additionalStaffDetails;
    //     }
    //     // echo "<pre>";print_r($staffTaxDetails);die();
    //     echo json_encode($staffTaxDetails);
    // }
}
